module.exports = {
  default: {
    paths: ['features/**/*.feature'],
    require: ['features/support/**/*.ts', 'features/steps/**/*.ts'],
    requireModule: ['ts-node/register'],
    format: [
      'progress-bar',
      'html:reports/cucumber-report.html',
      'json:reports/cucumber-report.json'
    ],
    parallel: 1,
    worldParameters: {
      headless: false // Default to headed mode
    }
  },
  chromium: {
    paths: ['features/**/*.feature'],
    require: ['features/support/**/*.ts', 'features/steps/**/*.ts'],
    requireModule: ['ts-node/register'],
    format: [
      'progress-bar',
      'html:reports/cucumber-chromium-report.html',
      'json:reports/cucumber-chromium-report.json',
      'json:allure-results/cucumber-chromium-results.json'
    ],
    parallel: 1,
    worldParameters: {
      headless: false,
      browser: 'chromium'
    }
  },
  firefox: {
    paths: ['features/**/*.feature'],
    require: ['features/support/**/*.ts', 'features/steps/**/*.ts'],
    requireModule: ['ts-node/register'],
    format: [
      'progress-bar',
      'html:reports/cucumber-firefox-report.html',
      'json:reports/cucumber-firefox-report.json'
    ],
    parallel: 1,
    worldParameters: {
      headless: false,
      browser: 'firefox'
    }
  },
  webkit: {
    paths: ['features/**/*.feature'],
    require: ['features/support/**/*.ts', 'features/steps/**/*.ts'],
    requireModule: ['ts-node/register'],
    format: [
      'progress-bar',
      'html:reports/cucumber-webkit-report.html',
      'json:reports/cucumber-webkit-report.json'
    ],
    parallel: 1,
    worldParameters: {
      headless: false,
      browser: 'webkit'
    }
  },
  trace: {
    paths: ['features/**/*.feature'],
    require: ['features/support/**/*.ts', 'features/steps/**/*.ts'],
    requireModule: ['ts-node/register'],
    format: [
      'progress-bar',
      'html:reports/cucumber-report.html',
      'json:reports/cucumber-report.json'
    ],
    parallel: 1,
    worldParameters: {
      headless: false,
      trace: true
    }
  },
  // Parallel execution profiles
  parallel: {
    paths: ['features/**/*.feature'],
    require: ['features/support/**/*.ts', 'features/steps/**/*.ts'],
    requireModule: ['ts-node/register'],
    format: [
      'progress-bar',
      'html:reports/cucumber-parallel-report.html',
      'json:reports/cucumber-parallel-report.json'
    ],
    parallel: 3, // Run 3 scenarios in parallel
    worldParameters: {
      headless: true // Use headless mode for parallel execution
    }
  },
  'parallel-chromium': {
    paths: ['features/**/*.feature'],
    require: ['features/support/**/*.ts', 'features/steps/**/*.ts'],
    requireModule: ['ts-node/register'],
    format: [
      'progress-bar',
      'html:reports/cucumber-parallel-chromium-report.html',
      'json:reports/cucumber-parallel-chromium-report.json'
    ],
    parallel: 3, // Run 3 scenarios in parallel
    worldParameters: {
      headless: true,
      browser: 'chromium'
    }
  },
  'parallel-firefox': {
    paths: ['features/**/*.feature'],
    require: ['features/support/**/*.ts', 'features/steps/**/*.ts'],
    requireModule: ['ts-node/register'],
    format: [
      'progress-bar',
      'html:reports/cucumber-parallel-firefox-report.html',
      'json:reports/cucumber-parallel-firefox-report.json'
    ],
    parallel: 3, // Run 3 scenarios in parallel
    worldParameters: {
      headless: true,
      browser: 'firefox'
    }
  },
  'parallel-webkit': {
    paths: ['features/**/*.feature'],
    require: ['features/support/**/*.ts', 'features/steps/**/*.ts'],
    requireModule: ['ts-node/register'],
    format: [
      'progress-bar',
      'html:reports/cucumber-parallel-webkit-report.html',
      'json:reports/cucumber-parallel-webkit-report.json'
    ],
    parallel: 3, // Run 3 scenarios in parallel
    worldParameters: {
      headless: true,
      browser: 'webkit'
    }
  }
}

