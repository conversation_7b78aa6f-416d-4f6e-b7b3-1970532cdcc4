# Test info

- Name: GROPR1 Login - Smoke & Regression Tests >> GROPR1 (User 1001) navigation and logout - Regression Test
- Location: /Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/ui-mode-tests/gropr1-smoke-regression.spec.ts:67:7

# Error details

```
Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/webkit-2158/pw_run.sh
╔═════════════════════════════════════════════════════════════════════════╗
║ Looks like Playwright Test or Playwright was just installed or updated. ║
║ Please run the following command to download new browsers:              ║
║                                                                         ║
║     npx playwright install                                              ║
║                                                                         ║
║ <3 Playwright Team                                                      ║
╚═════════════════════════════════════════════════════════════════════════╝
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | // GROPR1 Login Test for PROD environment with Smoke and Regression tags
   4 | // This mirrors the Cucumber test: user 1001 login with @Smoke @Regression @prod tags
   5 |
   6 | test.describe('GROPR1 Login - Smoke & Regression Tests', () => {
   7 |   
   8 |   test.beforeEach(async ({ page }) => {
   9 |     // Set longer timeout for all tests
  10 |     test.setTimeout(60000);
  11 |   });
  12 |
  13 |   test('GROPR1 (User 1001) can login successfully in PROD - Smoke Test', {
  14 |     tag: ['@smoke', '@regression', '@prod', '@user1001']
  15 |   }, async ({ page }) => {
  16 |     // Navigate to GRO login page
  17 |     await page.goto('/groNg/#/login');
  18 |
  19 |     // Wait for page to load
  20 |     await page.waitForLoadState('networkidle');
  21 |
  22 |     // Use GROPR1 credentials (User 1001)
  23 |     const username = 'GROPR1';
  24 |     const password = 'Testtest1';
  25 |
  26 |     // Fill in login form
  27 |     await page.locator('input[formcontrolname="userName"]').fill(username, { timeout: 10000 });
  28 |     await page.locator('input[formcontrolname="password"]').fill(password, { timeout: 10000 });
  29 |
  30 |     // Click login button
  31 |     await page.locator('button[type="submit"]').click({ timeout: 10000 });
  32 |
  33 |     // Wait for navigation to complete
  34 |     await page.waitForURL('**/home', { timeout: 30000 });
  35 |
  36 |     // Verify successful login - check for home page elements
  37 |     await expect(page.locator('h1')).toContainText('to live services & care options', { timeout: 15000 });
  38 |
  39 |     // Verify we're on the home page
  40 |     await expect(page).toHaveURL(/groNg\/#\/home/<USER>
  41 |
  42 |     // Take a screenshot for verification
  43 |     await page.screenshot({ path: 'test-results/gropr1-login-success.png', fullPage: true });
  44 |   });
  45 |
  46 |   test('GROPR1 (User 1001) login page loads correctly - Regression Test', {
  47 |     tag: ['@smoke', '@regression', '@prod', '@user1001']
  48 |   }, async ({ page }) => {
  49 |     // Navigate to GRO login page
  50 |     await page.goto('/groNg/#/login');
  51 |
  52 |     // Wait for page to load
  53 |     await page.waitForLoadState('networkidle');
  54 |
  55 |     // Verify login page elements are present
  56 |     await expect(page.locator('input[formcontrolname="userName"]')).toBeVisible();
  57 |     await expect(page.locator('input[formcontrolname="password"]')).toBeVisible();
  58 |     await expect(page.locator('button[type="submit"]')).toBeVisible();
  59 |
  60 |     // Verify page title
  61 |     await expect(page).toHaveTitle(/GuidanceResources/);
  62 |
  63 |     // Take a screenshot of the login page
  64 |     await page.screenshot({ path: 'test-results/gropr1-login-page.png', fullPage: true });
  65 |   });
  66 |
> 67 |   test('GROPR1 (User 1001) navigation and logout - Regression Test', {
     |       ^ Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/webkit-2158/pw_run.sh
  68 |     tag: ['@regression', '@prod', '@user1001']
  69 |   }, async ({ page }) => {
  70 |     // Login first
  71 |     await page.goto('/groNg/#/login');
  72 |     await page.waitForLoadState('networkidle');
  73 |
  74 |     const username = 'GROPR1';
  75 |     const password = 'Testtest1';
  76 |
  77 |     await page.locator('input[formcontrolname="userName"]').fill(username);
  78 |     await page.locator('input[formcontrolname="password"]').fill(password);
  79 |     await page.locator('button[type="submit"]').click();
  80 |     await page.waitForURL('**/home', { timeout: 30000 });
  81 |
  82 |     // Verify successful login
  83 |     await expect(page).toHaveURL(/groNg\/#\/home/<USER>
  84 |
  85 |     // Check if logout functionality is available (if logout button exists)
  86 |     const logoutButton = page.locator('a:has-text("Logout"), button:has-text("Logout"), [data-testid="logout"]');
  87 |     if (await logoutButton.count() > 0) {
  88 |       await logoutButton.click();
  89 |       await expect(page).toHaveURL(/login/);
  90 |     }
  91 |
  92 |     // Take a screenshot after logout
  93 |     await page.screenshot({ path: 'test-results/gropr1-after-logout.png', fullPage: true });
  94 |   });
  95 | });
  96 |
```