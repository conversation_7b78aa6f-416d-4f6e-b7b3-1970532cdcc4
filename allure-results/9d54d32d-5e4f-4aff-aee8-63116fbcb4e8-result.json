{"uuid": "9d54d32d-5e4f-4aff-aee8-63116fbcb4e8", "name": "GROPR1 (User 1001) login page loads correctly - Regression Test", "historyId": "643038a933ca43030c382fbae497242a:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "browser.newContext", "start": 1749080195249, "uuid": "301fb1c0-4c99-4658-8895-13b21ee47799", "stop": 1749080195256}], "attachments": [], "parameters": [], "name": "fixture: context", "start": 1749080195249, "uuid": "c1c6c4f8-317d-4bff-be81-d280cb26f4ce", "stop": 1749080195259}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "browserContext.newPage", "start": 1749080195261, "uuid": "e2ff0183-b809-45d4-8716-4df8a6b973ad", "stop": 1749080195664}], "attachments": [], "parameters": [], "name": "fixture: page", "start": 1749080195260, "uuid": "6e1b8d56-0261-4146-8fdf-3d883f6665fe", "stop": 1749080195664}], "attachments": [], "parameters": [], "name": "beforeEach hook", "start": 1749080195247, "uuid": "984a2690-66d7-43b9-98fd-051aea51f614", "stop": 1749080195665}], "attachments": [], "parameters": [], "name": "Before Hooks", "start": 1749080195247, "uuid": "740bb579-1893-4d5b-94f0-ba26e55877af", "stop": 1749080195665}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080195667, "name": "page.goto(/groNg/#/login)", "uuid": "41ddf1b4-fc89-498b-9c91-8dcbd9962068", "stop": 1749080197022}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080197024, "name": "page.waitForLoadState", "uuid": "2ba144bf-4044-4be8-b765-ddb62d0b0b8a", "stop": 1749080198103}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080198104, "name": "expect.toBeVisible", "uuid": "2c47a72e-849b-4777-8c97-d729a4ba5931", "stop": 1749080198138}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080198139, "name": "expect.toBeVisible", "uuid": "fc3421fb-64e1-4b66-94ca-eebf71c2306f", "stop": 1749080198149}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080198149, "name": "expect.toBeVisible", "uuid": "c2b8445b-ec9e-4c1c-84fc-38f78ab58887", "stop": 1749080198159}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080198159, "name": "expect.toHaveTitle", "uuid": "e3e416b8-61e1-4466-9a92-9e906d1da8ba", "stop": 1749080198170}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080198171, "name": "page.screenshot", "uuid": "343036bc-5cf4-42f6-9fe2-16fa58535f6d", "stop": 1749080198525}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "fixture: page", "start": 1749080198529, "uuid": "332f8a92-43aa-4fe0-8bcb-48152c4ab5dc", "stop": 1749080198529}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "fixture: context", "start": 1749080198530, "uuid": "64db53be-70b7-40de-b3eb-b7a012b77efb", "stop": 1749080198530}], "attachments": [], "parameters": [], "name": "After Hooks", "start": 1749080198528, "uuid": "0d136bcb-c915-4fc8-a620-f20d118b31fc", "stop": 1749080198897}], "attachments": [], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "gropr1-smoke-regression.spec.ts"}, {"name": "titlePath", "value": " > chromium > gropr1-smoke-regression.spec.ts > GROPR1 Login - Smoke & Regression Tests"}, {"name": "tag", "value": "smoke"}, {"name": "tag", "value": "regression"}, {"name": "tag", "value": "prod"}, {"name": "tag", "value": "user1001"}, {"name": "host", "value": "Hamdans-MacBook-Pro.local"}, {"name": "thread", "value": "pid-61562-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "gropr1-smoke-regression.spec.ts"}, {"name": "subSuite", "value": "GROPR1 Login - Smoke & Regression Tests"}], "links": [], "start": 1749080195257, "testCaseId": "643038a933ca43030c382fbae497242a", "fullName": "gropr1-smoke-regression.spec.ts:46:7", "stop": 1749080198902}