{"uuid": "f54da0b1-6105-4935-a789-5f4b6732ba4c", "name": "GROPR1 (User 1001) navigation and logout - Regression Test", "historyId": "abab5ea59f4948e39acf97175e0a5992:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "browser.newContext", "start": 1749080198903, "uuid": "a05ae568-5d72-4f9c-8ab3-2beb427d5f3f", "stop": 1749080198914}], "attachments": [], "parameters": [], "name": "fixture: context", "start": 1749080198902, "uuid": "2cd8bf68-48d7-4bbe-a70a-049e9d8a584f", "stop": 1749080198919}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "browserContext.newPage", "start": 1749080198921, "uuid": "13f6d8b7-cc8a-4d1d-a2c7-5068946e3e07", "stop": 1749080199315}], "attachments": [], "parameters": [], "name": "fixture: page", "start": 1749080198920, "uuid": "5200762a-0d19-42ae-a898-e87b077d0f51", "stop": 1749080199315}], "attachments": [], "parameters": [], "name": "beforeEach hook", "start": 1749080198900, "uuid": "470972af-31bc-4215-8f52-9beff3c79db7", "stop": 1749080199315}], "attachments": [], "parameters": [], "name": "Before Hooks", "start": 1749080198899, "uuid": "f1dd55be-5043-4cad-803a-ac830890731f", "stop": 1749080199315}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080199317, "name": "page.goto(/groNg/#/login)", "uuid": "f3ce08ed-d85d-41ce-8067-fd0bd2abd11a", "stop": 1749080200482}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080200484, "name": "page.waitForLoadState", "uuid": "e42a0b67-ebd7-40a8-bbc9-f962e3a25e60", "stop": 1749080201587}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080201588, "name": "locator.fill(GROPR1)", "uuid": "61609135-f34a-4a13-85f3-d1aa2e32d326", "stop": 1749080201745}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080201747, "name": "locator.fill(Testtest1)", "uuid": "3650e0b2-7b7d-4d4d-9e79-613f479aa4bb", "stop": 1749080202027}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080202030, "name": "locator.click(button[type=\"submit\"])", "uuid": "58a5556e-ac05-47d0-aee1-1a017fbb83f9", "stop": 1749080203199}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080203204, "name": "page.waitForURL", "uuid": "358179ba-2b12-4ec8-bbcc-5f817d64604e", "stop": 1749080204327}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080204329, "name": "expect.toHaveURL", "uuid": "b50ebc2a-ed61-4748-8568-c98b1ad320a6", "stop": 1749080204370}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080204372, "name": "locator.count(a:has-text(\"Logout\"), button:has-text(\"Logout\"), [data-testid=\"logout\"])", "uuid": "145dfd49-cf46-4800-b99e-5e7b3451e4b8", "stop": 1749080204385}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080204386, "name": "page.screenshot", "uuid": "1bef7a57-8913-486b-8e5d-e47450744ca7", "stop": 1749080204869}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "fixture: page", "start": 1749080204873, "uuid": "8aedd53a-379e-4e33-9deb-e64f865c1002", "stop": 1749080204873}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "fixture: context", "start": 1749080204873, "uuid": "a9f32475-3756-41d3-a916-addaa1b66fb4", "stop": 1749080204873}], "attachments": [], "parameters": [], "name": "After Hooks", "start": 1749080204872, "uuid": "330ce4fb-55e6-4400-acef-d9bd4a0ab364", "stop": 1749080205284}], "attachments": [], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "gropr1-smoke-regression.spec.ts"}, {"name": "titlePath", "value": " > chromium > gropr1-smoke-regression.spec.ts > GROPR1 Login - Smoke & Regression Tests"}, {"name": "tag", "value": "regression"}, {"name": "tag", "value": "prod"}, {"name": "tag", "value": "user1001"}, {"name": "host", "value": "Hamdans-MacBook-Pro.local"}, {"name": "thread", "value": "pid-61562-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "gropr1-smoke-regression.spec.ts"}, {"name": "subSuite", "value": "GROPR1 Login - Smoke & Regression Tests"}], "links": [], "start": 1749080198907, "testCaseId": "abab5ea59f4948e39acf97175e0a5992", "fullName": "gropr1-smoke-regression.spec.ts:67:7", "stop": 1749080205287}