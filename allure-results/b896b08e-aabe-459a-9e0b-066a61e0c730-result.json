{"uuid": "b896b08e-aabe-459a-9e0b-066a61e0c730", "name": "GROPR1 (User 1001) navigation and logout - Regression Test", "historyId": "abab5ea59f4948e39acf97175e0a5992:b444eb0fbe6390c71e68b51dd25701fc", "status": "failed", "statusDetails": {"message": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/firefox-1482/firefox/Nightly.app/Contents/MacOS/firefox\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "trace": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/firefox-1482/firefox/Nightly.app/Contents/MacOS/firefox\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/firefox-1482/firefox/Nightly.app/Contents/MacOS/firefox\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "trace": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/firefox-1482/firefox/Nightly.app/Contents/MacOS/firefox\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/firefox-1482/firefox/Nightly.app/Contents/MacOS/firefox\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "trace": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/firefox-1482/firefox/Nightly.app/Contents/MacOS/firefox\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/firefox-1482/firefox/Nightly.app/Contents/MacOS/firefox\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "trace": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/firefox-1482/firefox/Nightly.app/Contents/MacOS/firefox\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/firefox-1482/firefox/Nightly.app/Contents/MacOS/firefox\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "trace": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/firefox-1482/firefox/Nightly.app/Contents/MacOS/firefox\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "browserType.launch", "start": 1749080208818, "uuid": "4ebdcf34-4066-416b-bba0-42377c3792f1", "stop": 1749080208830}], "attachments": [], "parameters": [], "name": "fixture: browser", "start": 1749080208815, "uuid": "7d8b87bd-6334-4f85-843f-938015a05276", "stop": 1749080208831}], "attachments": [], "parameters": [], "name": "beforeEach hook", "start": 1749080208808, "uuid": "4cac65e0-0af1-4f1a-8d47-deb6939881c3", "stop": 1749080208831}], "attachments": [], "parameters": [], "name": "Before Hooks", "start": 1749080208807, "uuid": "ce0d1d90-9db0-4817-9765-923656c9239a", "stop": 1749080208831}, {"statusDetails": {}, "stage": "finished", "steps": [], "attachments": [{"name": "_error-context-0", "source": "e2859e92-52ae-4b38-888f-acd9210cd131-attachment.md", "type": "text/markdown"}], "parameters": [], "start": 1749080208850, "name": "_error-context-0", "stop": 1749080208850}, {"statusDetails": {}, "stage": "finished", "steps": [], "attachments": [{"name": "trace", "source": "3dc76289-cb8c-4304-a56b-d11ff9aac160-attachment.zip", "type": "application/vnd.allure.playwright-trace"}], "parameters": [], "start": 1749080208856, "name": "trace", "stop": 1749080208856}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "After Hooks", "start": 1749080208832, "uuid": "15069e38-f826-401d-9683-1583664c3476", "stop": 1749080208837}], "attachments": [], "parameters": [{"name": "Project", "value": "firefox"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "gropr1-smoke-regression.spec.ts"}, {"name": "titlePath", "value": " > firefox > gropr1-smoke-regression.spec.ts > GROPR1 Login - Smoke & Regression Tests"}, {"name": "tag", "value": "regression"}, {"name": "tag", "value": "prod"}, {"name": "tag", "value": "user1001"}, {"name": "host", "value": "Hamdans-MacBook-Pro.local"}, {"name": "thread", "value": "pid-61562-worker-0"}, {"name": "parentSuite", "value": "firefox"}, {"name": "suite", "value": "gropr1-smoke-regression.spec.ts"}, {"name": "subSuite", "value": "GROPR1 Login - Smoke & Regression Tests"}], "links": [], "start": 1749080208805, "testCaseId": "abab5ea59f4948e39acf97175e0a5992", "fullName": "gropr1-smoke-regression.spec.ts:67:7", "stop": 1749080208810}