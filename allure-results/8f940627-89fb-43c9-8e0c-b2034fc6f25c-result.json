{"uuid": "8f940627-89fb-43c9-8e0c-b2034fc6f25c", "name": "GROPR1 (User 1001) can login successfully in PROD - Smoke Test", "historyId": "ed87e8e24c199585e0824ad8162b107b:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "browserType.launch", "start": 1749080185491, "uuid": "603f7a8e-a02c-4971-a8cd-25bfa7896efe", "stop": 1749080186221}], "attachments": [], "parameters": [], "name": "fixture: browser", "start": 1749080185488, "uuid": "909cd364-7582-4925-bd84-7b04c20ac94d", "stop": 1749080186222}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "browser.newContext", "start": 1749080186228, "uuid": "1b01ac1a-da68-42a8-97f3-8797a20f6b86", "stop": 1749080186249}], "attachments": [], "parameters": [], "name": "fixture: context", "start": 1749080186224, "uuid": "24a1b5f4-0379-4e67-a575-72ded6a15967", "stop": 1749080186256}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "browserContext.newPage", "start": 1749080186258, "uuid": "33c3e167-7151-4794-bf7d-7b17125f80b9", "stop": 1749080187426}], "attachments": [], "parameters": [], "name": "fixture: page", "start": 1749080186256, "uuid": "3ed3f2b6-1400-4b9f-aebd-e9eb413f686a", "stop": 1749080187426}], "attachments": [], "parameters": [], "name": "beforeEach hook", "start": 1749080185482, "uuid": "64aef0af-1dae-4355-9952-b4e31b0c19da", "stop": 1749080187426}], "attachments": [], "parameters": [], "name": "Before Hooks", "start": 1749080185481, "uuid": "76a68759-f0f1-4000-8453-c8b0b78e4e8c", "stop": 1749080187426}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080187429, "name": "page.goto(/groNg/#/login)", "uuid": "7d737cb2-0b97-4887-ab73-745110611e2a", "stop": 1749080188715}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080188717, "name": "page.waitForLoadState", "uuid": "6757817f-904b-422f-98cf-ab813ef9266d", "stop": 1749080189753}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080189756, "name": "locator.fill(GROPR1)", "uuid": "940339e9-d275-411d-8d1e-7eafbd0f0b32", "stop": 1749080189957}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080189959, "name": "locator.fill(Testtest1)", "uuid": "ba6ca73e-3021-4cd4-b28e-c7d88b2a077f", "stop": 1749080190176}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080190178, "name": "locator.click(button[type=\"submit\"])", "uuid": "cc19a1dc-f5a0-4f23-a130-d5db1a928395", "stop": 1749080190694}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080190696, "name": "page.waitForURL", "uuid": "0a9dc497-11dd-42ee-ab3a-34db039f5983", "stop": 1749080192651}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080192656, "name": "expect.toContainText", "uuid": "a77b0fed-200a-4f06-a263-245fc9dec7a5", "stop": 1749080192692}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080192694, "name": "expect.toHaveURL", "uuid": "cd555fc6-942b-4b55-bf85-7d6205e16f07", "stop": 1749080192725}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1749080192727, "name": "page.screenshot", "uuid": "e1acb156-7e05-419f-99bb-3918ebed6501", "stop": 1749080194557}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "fixture: page", "start": 1749080194563, "uuid": "99fbd055-0492-4d41-82d2-e6a9680628a3", "stop": 1749080194564}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "fixture: context", "start": 1749080194564, "uuid": "feccc22e-32fe-4bea-815c-ebd2db627eab", "stop": 1749080194564}], "attachments": [], "parameters": [], "name": "After Hooks", "start": 1749080194561, "uuid": "d84be682-b5b8-4dd7-a044-e13b326f4b1f", "stop": 1749080195245}], "attachments": [], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "gropr1-smoke-regression.spec.ts"}, {"name": "titlePath", "value": " > chromium > gropr1-smoke-regression.spec.ts > GROPR1 Login - Smoke & Regression Tests"}, {"name": "tag", "value": "smoke"}, {"name": "tag", "value": "regression"}, {"name": "tag", "value": "prod"}, {"name": "tag", "value": "user1001"}, {"name": "host", "value": "Hamdans-MacBook-Pro.local"}, {"name": "thread", "value": "pid-61562-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "suite", "value": "gropr1-smoke-regression.spec.ts"}, {"name": "subSuite", "value": "GROPR1 Login - Smoke & Regression Tests"}], "links": [], "start": 1749080185480, "testCaseId": "ed87e8e24c199585e0824ad8162b107b", "fullName": "gropr1-smoke-regression.spec.ts:13:7", "stop": 1749080194502}