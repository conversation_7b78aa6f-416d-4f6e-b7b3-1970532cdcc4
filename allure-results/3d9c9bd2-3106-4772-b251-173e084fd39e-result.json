{"uuid": "3d9c9bd2-3106-4772-b251-173e084fd39e", "name": "GROPR1 (User 1001) login page loads correctly - Regression Test", "historyId": "643038a933ca43030c382fbae497242a:b444eb0fbe6390c71e68b51dd25701fc", "status": "failed", "statusDetails": {"message": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/firefox-1482/firefox/Nightly.app/Contents/MacOS/firefox\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "trace": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/firefox-1482/firefox/Nightly.app/Contents/MacOS/firefox\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/firefox-1482/firefox/Nightly.app/Contents/MacOS/firefox\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "trace": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/firefox-1482/firefox/Nightly.app/Contents/MacOS/firefox\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/firefox-1482/firefox/Nightly.app/Contents/MacOS/firefox\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "trace": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/firefox-1482/firefox/Nightly.app/Contents/MacOS/firefox\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/firefox-1482/firefox/Nightly.app/Contents/MacOS/firefox\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "trace": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/firefox-1482/firefox/Nightly.app/Contents/MacOS/firefox\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/firefox-1482/firefox/Nightly.app/Contents/MacOS/firefox\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "trace": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/firefox-1482/firefox/Nightly.app/Contents/MacOS/firefox\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "browserType.launch", "start": 1749080207823, "uuid": "cda6d9e5-dc82-4de6-8923-aa3f7220056b", "stop": 1749080207829}], "attachments": [], "parameters": [], "name": "fixture: browser", "start": 1749080207820, "uuid": "4f657b00-bc51-43db-b776-67ed5980ef53", "stop": 1749080207830}], "attachments": [], "parameters": [], "name": "beforeEach hook", "start": 1749080207804, "uuid": "a3bf749d-1a69-401c-aa7e-e4a3bf8b4918", "stop": 1749080207830}], "attachments": [], "parameters": [], "name": "Before Hooks", "start": 1749080207803, "uuid": "8186ec37-6e72-43af-87c0-c565fd9974fe", "stop": 1749080207830}, {"statusDetails": {}, "stage": "finished", "steps": [], "attachments": [{"name": "_error-context-0", "source": "400e53b3-212b-4410-a802-c88e21d07e62-attachment.md", "type": "text/markdown"}], "parameters": [], "start": 1749080207852, "name": "_error-context-0", "stop": 1749080207852}, {"statusDetails": {}, "stage": "finished", "steps": [], "attachments": [{"name": "trace", "source": "e663aee8-80dd-4f3a-843f-d7876b882441-attachment.zip", "type": "application/vnd.allure.playwright-trace"}], "parameters": [], "start": 1749080207859, "name": "trace", "stop": 1749080207859}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "After Hooks", "start": 1749080207831, "uuid": "d6f755ac-9ff4-4857-b0fb-e8ee9cbefaa7", "stop": 1749080207837}], "attachments": [], "parameters": [{"name": "Project", "value": "firefox"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "gropr1-smoke-regression.spec.ts"}, {"name": "titlePath", "value": " > firefox > gropr1-smoke-regression.spec.ts > GROPR1 Login - Smoke & Regression Tests"}, {"name": "tag", "value": "smoke"}, {"name": "tag", "value": "regression"}, {"name": "tag", "value": "prod"}, {"name": "tag", "value": "user1001"}, {"name": "host", "value": "Hamdans-MacBook-Pro.local"}, {"name": "thread", "value": "pid-61562-worker-0"}, {"name": "parentSuite", "value": "firefox"}, {"name": "suite", "value": "gropr1-smoke-regression.spec.ts"}, {"name": "subSuite", "value": "GROPR1 Login - Smoke & Regression Tests"}], "links": [], "start": 1749080207795, "testCaseId": "643038a933ca43030c382fbae497242a", "fullName": "gropr1-smoke-regression.spec.ts:46:7", "stop": 1749080207804}