{"uuid": "2977e91f-26d7-4ef1-9963-2cfd05dcac02", "name": "GROPR1 (User 1001) navigation and logout - Regression Test", "historyId": "abab5ea59f4948e39acf97175e0a5992:84e28e814b821ed013329cc8dbc467e0", "status": "failed", "statusDetails": {"message": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/webkit-2158/pw_run.sh\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "trace": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/webkit-2158/pw_run.sh\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/webkit-2158/pw_run.sh\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "trace": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/webkit-2158/pw_run.sh\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/webkit-2158/pw_run.sh\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "trace": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/webkit-2158/pw_run.sh\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/webkit-2158/pw_run.sh\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "trace": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/webkit-2158/pw_run.sh\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/webkit-2158/pw_run.sh\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "trace": "Error: browserType.launch: Executable doesn't exist at /Users/<USER>/Library/Caches/ms-playwright/webkit-2158/pw_run.sh\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "browserType.launch", "start": 1749080211862, "uuid": "73b8acc9-39f2-4fa8-804e-f7b9905d94d2", "stop": 1749080211869}], "attachments": [], "parameters": [], "name": "fixture: browser", "start": 1749080211857, "uuid": "4fe7e4c3-16af-4a85-8b24-f6777573b747", "stop": 1749080211870}], "attachments": [], "parameters": [], "name": "beforeEach hook", "start": 1749080211841, "uuid": "93b19e86-6b49-4df1-8d86-b1f4a580debe", "stop": 1749080211870}], "attachments": [], "parameters": [], "name": "Before Hooks", "start": 1749080211840, "uuid": "46493ad3-2d0e-4a55-922f-deee76c03008", "stop": 1749080211870}, {"statusDetails": {}, "stage": "finished", "steps": [], "attachments": [{"name": "_error-context-0", "source": "0916439f-679f-4d4c-b584-3d231bd12278-attachment.md", "type": "text/markdown"}], "parameters": [], "start": 1749080211891, "name": "_error-context-0", "stop": 1749080211891}, {"statusDetails": {}, "stage": "finished", "steps": [], "attachments": [{"name": "trace", "source": "caf25826-1c6c-4210-97c0-63ef61502e4e-attachment.zip", "type": "application/vnd.allure.playwright-trace"}], "parameters": [], "start": 1749080211899, "name": "trace", "stop": 1749080211899}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "After Hooks", "start": 1749080211871, "uuid": "f939d2a9-cb34-413e-aef0-cd124224cf7d", "stop": 1749080211876}], "attachments": [], "parameters": [{"name": "Project", "value": "webkit"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "gropr1-smoke-regression.spec.ts"}, {"name": "titlePath", "value": " > webkit > gropr1-smoke-regression.spec.ts > GROPR1 Login - Smoke & Regression Tests"}, {"name": "tag", "value": "regression"}, {"name": "tag", "value": "prod"}, {"name": "tag", "value": "user1001"}, {"name": "host", "value": "Hamdans-MacBook-Pro.local"}, {"name": "thread", "value": "pid-61562-worker-0"}, {"name": "parentSuite", "value": "webkit"}, {"name": "suite", "value": "gropr1-smoke-regression.spec.ts"}, {"name": "subSuite", "value": "GROPR1 Login - Smoke & Regression Tests"}], "links": [], "start": 1749080211830, "testCaseId": "abab5ea59f4948e39acf97175e0a5992", "fullName": "gropr1-smoke-regression.spec.ts:67:7", "stop": 1749080211844}