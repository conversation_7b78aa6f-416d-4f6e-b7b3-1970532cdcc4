Feature: User Registration
    @RegistrationSanity @DynamicOrgTest @Regression @dev @stg @prod
  Scenario Outline: User registers with different organization web IDs
    Given the user navigates to the registration login page
    When the user provides the organization web ID "<organizationId>" and clicks Register
    Then the user is taken to the registration profile page
    When the user fills out the registration form
    Then the user is able to logout and log back in with the new account

    Examples:
      | organizationId  |
      | RTX             |
      | cpwellbeing     |