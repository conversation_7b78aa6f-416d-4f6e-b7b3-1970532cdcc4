Feature: Multi-Account Login
	@LoginMultiAccount @Regression
	Scenario Outline: User logs in with different accounts
    Given access to GRO login page "/groNg/#/login"
    When user logs in with WebID "<webId>"
    Then user should be logged in successfully
    And user should see the home page

    Examples:
      | webId       |
      | RTX         |
      | pnc         |
      | aon         |
      | teconn      |
      | MacysIncEAP |





# WebIds above will be added for different locales and in org specifications