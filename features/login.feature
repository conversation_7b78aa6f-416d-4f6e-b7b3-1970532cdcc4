Feature: Login to GRO


  @1001  @Smoke @Regression @dev @stg @prod @login
  Scenario: Successful login using data ID 1001
    Given access to GRO login page "/groNg/#/login"
    When user logs in using test data ID "1001"
    Then user should be logged in successfully

  @1002  @Smoke @Regression @dev @stg @prod @login
  Scenario: <PERSON><PERSON> fails with invalid username from ID 1002
    Given access to GRO login page "/groNg/#/login"
    When user logs in using test data ID "1002"
    Then user should see an error message

  @1003  @Smoke @Regression @dev @stg @prod @login
  Scenario: <PERSON><PERSON> fails with invalid password from ID 1003
    Given access to GRO login page "/groNg/#/login"
    When user logs in using test data ID "1003"
    Then user should see an error message


