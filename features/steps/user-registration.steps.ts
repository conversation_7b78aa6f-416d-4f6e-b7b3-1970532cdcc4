import { Given, When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { generateRegistrationData } from '../support/testDataGenerator';
import { loadEnvironmentConfig, loadCredentials, loadRegistrationDefaults } from '../support/envLoader';

interface RegistrationData {
  username: string;
  password: string;
  email: string;
  firstName: string;
  lastName: string;
  empId: string;
  phoneNo: string;
  zipcode: string;
}

const env = process.env.TEST_ENV || 'prod';  // default to 'prod'

const envConfig = loadEnvironmentConfig(env);
const credentials = loadCredentials(env);
const registrationDefaults = loadRegistrationDefaults();

let regData: RegistrationData;

Given('the user navigates to the registration login page', async function() {
  await this.page.goto(`${envConfig.baseUrl}/groQa/#/login`);
});

When('the user provides the organization web ID {string} and clicks Register', async function(orgId: string) {
  await this.page.fill('input[name="orgId"]', orgId);
  await this.page.click('button:has-text("Register")');
});

Then('the user is taken to the registration profile page', async function() {
  await expect(this.page.locator('h1')).toContainText('Registration: Profile');
});

When('the user fills out the registration form', async function() {
  const dynamicData = generateRegistrationData();
  regData = { ...registrationDefaults, ...dynamicData };

  await this.page.fill('input[name="username"]', regData.username);
  await this.page.fill('input[name="password"]', regData.password);
  await this.page.fill('input[name="confirmPassword"]', regData.password);
  await this.page.fill('input[name="email"]', regData.email);
  await this.page.fill('input[name="confirmEmail"]', regData.email);
  await this.page.fill('input[name="firstName"]', regData.firstName);
  await this.page.fill('input[name="lastName"]', regData.lastName);
  await this.page.fill('input[name="empId"]', regData.empId);
  await this.page.fill('input[name="phone"]', regData.phoneNo);
  await this.page.fill('input[name="zipcode"]', regData.zipcode);
  await this.page.check('input[name="termsAccepted"]');
  await this.page.check('input[name="ageConfirmed"]');
  await this.page.click('button:has-text("Submit")');
});

Then('the user is able to logout and log back in with the new account', async function() {
  await this.page.click('a:has-text("Logout")');
  await this.page.fill('input[name="username"]', regData.username);
  await this.page.fill('input[name="password"]', regData.password);
  await this.page.click('button:has-text("Login")');
  await expect(this.page.locator('h1')).toContainText('Welcome');
});
