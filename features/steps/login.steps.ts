import { Given, When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { getLoginDataById } from '../support/loginDataLoader';
import * as fs from 'fs';

// Dynamically load the environment (e.g., 'prod', 'dev', 'stg')
const env = process.env.TEST_ENV || 'prod';

// Load base URL for environment
const envConfigPath = `./config/environments/${env}.json`;
const baseUrl = JSON.parse(fs.readFileSync(envConfigPath, 'utf-8')).baseUrl;

let currentUser;

Given('access to GRO login page {string}', { timeout: 30000 }, async function (urlPath: string) {
  await this.page.goto(`${baseUrl}${urlPath}`);
});

When('user enters valid login data for ID {string}', { timeout: 30000 }, async function (id: string) {
  currentUser = getLoginDataById(id, env);

  // Wait for page to load
  await this.page.waitForLoadState('networkidle');

  // Use the correct selectors based on the UI mode tests
  await this.page.fill('input[formcontrolname="userName"]', currentUser.username);
  await this.page.fill('input[formcontrolname="password"]', currentUser.password);
  await this.page.click('button[type="submit"]');

  // Wait for navigation to complete
  await this.page.waitForURL('**/home', { timeout: 30000 });
});

When('user logs in using test data ID {string}', { timeout: 30000 }, async function (id: string) {
  currentUser = getLoginDataById(id, env);

  // Wait for page to load
  await this.page.waitForLoadState('networkidle');

  // Fill in login credentials
  await this.page.fill('input[formcontrolname="userName"]', currentUser.username);
  await this.page.fill('input[formcontrolname="password"]', currentUser.password);
  await this.page.click('button[type="submit"]');

  // Wait a moment for the response (don't wait for navigation as it might fail for invalid credentials)
  await this.page.waitForTimeout(3000);
});

Then('user should be logged in successfully', { timeout: 30000 }, async function () {
  await expect(this.page.locator('text=Welcome')).toBeVisible();
});

Then('user should see the home page', { timeout: 30000 }, async function () {
  // Check for successful navigation to home page - look for elements that indicate successful login
  await expect(this.page.locator('h1')).toContainText('to live services & care options');
});

Then('user should see an error message', { timeout: 30000 }, async function () {
  // Check for error message or failed login indication
  // Common error selectors for login failures
  const errorSelectors = [
    '.error-message',
    '.alert-danger',
    '.login-error',
    '[data-testid="error-message"]',
    '.validation-error',
    '.form-error',
    'div:has-text("Invalid")',
    'div:has-text("incorrect")',
    'div:has-text("failed")',
    'div:has-text("error")'
  ];

  let errorFound = false;
  for (const selector of errorSelectors) {
    try {
      const errorElement = this.page.locator(selector);
      if (await errorElement.count() > 0 && await errorElement.isVisible()) {
        errorFound = true;
        break;
      }
    } catch (e) {
      // Continue to next selector
    }
  }

  // If no specific error message found, check if we're still on login page (indicating failed login)
  if (!errorFound) {
    const currentUrl = this.page.url();
    if (currentUrl.includes('login')) {
      errorFound = true; // Still on login page indicates failed login
    }
  }

  // Assert that we found an error indication
  expect(errorFound).toBe(true);
});

