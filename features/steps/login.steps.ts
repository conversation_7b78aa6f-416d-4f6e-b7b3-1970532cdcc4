import { Given, When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { getLoginDataById } from '../support/loginDataLoader';
import * as fs from 'fs';

// Dynamically load the environment (e.g., 'prod', 'dev', 'stg')
const env = process.env.TEST_ENV || 'prod';

// Load base URL for environment
const envConfigPath = `./config/environments/${env}.json`;
const baseUrl = JSON.parse(fs.readFileSync(envConfigPath, 'utf-8')).baseUrl;

let currentUser;

Given('access to GRO login page {string}', { timeout: 30000 }, async function (urlPath: string) {
  await this.page.goto(`${baseUrl}${urlPath}`);
});

When('user enters valid login data for ID {string}', { timeout: 30000 }, async function (id: string) {
  currentUser = getLoginDataById(id, env);

  // Wait for page to load
  await this.page.waitForLoadState('networkidle');

  // Use the correct selectors based on the UI mode tests
  await this.page.fill('input[formcontrolname="userName"]', currentUser.username);
  await this.page.fill('input[formcontrolname="password"]', currentUser.password);
  await this.page.click('button[type="submit"]');

  // Wait for navigation to complete
  await this.page.waitForURL('**/home', { timeout: 30000 });
});

Then('user should be logged in successfully', { timeout: 30000 }, async function () {
  await expect(this.page.locator('text=Welcome')).toBeVisible();
});

Then('user should see the home page', { timeout: 30000 }, async function () {
  // Check for successful navigation to home page - look for elements that indicate successful login
  await expect(this.page.locator('h1')).toContainText('to live services & care options');
});

