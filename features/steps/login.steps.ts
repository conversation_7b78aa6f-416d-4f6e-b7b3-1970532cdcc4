import { Given, When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { getLoginDataById } from '../support/loginDataLoader';
import * as fs from 'fs';

// Dynamically load the environment (e.g., 'prod', 'dev', 'stg')
const env = process.env.TEST_ENV || 'prod';

// Load base URL for environment
const envConfigPath = `./config/environments/${env}.json`;
const baseUrl = JSON.parse(fs.readFileSync(envConfigPath, 'utf-8')).baseUrl;

let currentUser;

Given('access to GRO login page {string}', async function (urlPath: string) {
  await this.page.goto(`${baseUrl}${urlPath}`);
});

When('user enters valid login data for ID {string}', async function (id: string) {
  currentUser = getLoginDataById(id, env);

  await this.page.fill('input[name="username"]', currentUser.username);
  await this.page.fill('input[name="password"]', currentUser.password);
  await this.page.click('button:has-text("Login")');
});

Then('user should be logged in successfully', async function () {
  await expect(this.page.locator('text=Welcome')).toBeVisible();
});

Then('user should see the home page', async function () {
  await expect(this.page.locator('h1')).toContainText('Home');
});

