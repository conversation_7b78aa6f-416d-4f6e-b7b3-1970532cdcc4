import { Given, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import * as fs from 'fs';

const env = process.env.TEST_ENV || 'prod';
const config = JSON.parse(fs.readFileSync(`./config/environments/${env}.json`, 'utf-8'));

Given('I navigate to the homepage', async function () {
  await this.page.goto(`${config.baseUrl}/groNg/#/login`);
  await this.page.waitForLoadState('networkidle');
});

Then('I should see the welcome header', async function () {
  await expect(this.page.locator('text=Welcome To')).toBeVisible();
  await expect(this.page.locator('text=ComPsych GuidanceResources')).toBeVisible();
});

Then('I should see the username field', async function () {
  await expect(this.page.locator('input[name="username"]')).toBeVisible();
});

Then('I should see the password field', async function () {
  await expect(this.page.locator('input[name="password"]')).toBeVisible();
});

Then('I should see the Login button', async function () {
  await expect(this.page.locator('button:has-text("Login")')).toBeVisible();
});

Then('I should see the Register button', async function () {
  await expect(this.page.locator('button:has-text("Register")')).toBeVisible();
});

Then('I should see the Remember Me option', async function () {
  await expect(this.page.locator('text=Remember Me')).toBeVisible();
});

Then('I should see the Forgot links', async function () {
  await expect(this.page.locator('text=Forgot Username?')).toBeVisible();
  await expect(this.page.locator('text=Forgot Password?')).toBeVisible();
});

Then('I should see the mobile app badges', async function () {
  await expect(this.page.locator('img[alt="Get it on Google Play"]')).toBeVisible();
  await expect(this.page.locator('img[alt="Download on the App Store"]')).toBeVisible();
});


