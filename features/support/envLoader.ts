import * as fs from 'fs';

interface EnvironmentConfig {
  baseUrl: string;
}

interface Credentials {
  username: string;
  password: string;
  webId?: string;
}

export function loadEnvironmentConfig(env: string): EnvironmentConfig {
  const filePath = `./config/environments/${env}.json`;
  if (!fs.existsSync(filePath)) {
    throw new Error(`Environment config file not found: ${filePath}`);
  }
  return JSON.parse(fs.readFileSync(filePath, 'utf-8'));
}

export function loadCredentials(env: string): Credentials[] {
  const filePath = `./config/credentials/${env}-credentials.json`;
  if (!fs.existsSync(filePath)) {
    throw new Error(`Credentials file not found: ${filePath}`);
  }
  return JSON.parse(fs.readFileSync(filePath, 'utf-8'));
}

export function loadRegistrationDefaults(): object {
  const filePath = `./config/test-data/registration-default.json`;
  if (!fs.existsSync(filePath)) {
    throw new Error(`Registration default file not found: ${filePath}`);
  }
  return JSON.parse(fs.readFileSync(filePath, 'utf-8'));
}
