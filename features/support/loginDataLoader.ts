import * as fs from 'fs';
import * as path from 'path';

interface LoginUser {
  username: string;
  password: string;
  webId: string;
}

export function getLoginDataById(id: string, env: string = 'prod'): LoginUser {
  const filePath = path.resolve(__dirname, `../../config/credentials/${env}-credentials.json`);

  if (!fs.existsSync(filePath)) {
    throw new Error(`Credential file not found at ${filePath}`);
  }

  const users = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
  const user = users[id];

  if (!user) {
    throw new Error(`No login user found for ID "${id}" in ${env}-credentials.json`);
  }

  return user;
}

