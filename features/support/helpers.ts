import { Page } from '@playwright/test';
import * as path from 'path';
import * as fs from 'fs';

// Define credential interface
interface Credential {
  Username: string;
  WebID: string;
  Password: string;
}

// Load test environment configuration
const ENV = process.env.TEST_ENV || 'dev';
let testConfig: any = {};
let testCredentials: Credential[] = [];

// Load test credentials
try {
  const credentialsPath = path.resolve(__dirname, '../../config/test-credentials.json');
  const credentialsData = fs.readFileSync(credentialsPath, 'utf8');
  testCredentials = JSON.parse(credentialsData);
  console.log(`Loaded ${testCredentials.length} test credentials`);
} catch (error) {
  console.warn('Failed to load test credentials. Using default values.');
  testCredentials = [
    {
      Username: 'rayth11',
      WebID: 'RTX',
      Password: 'Testtest2'
    }
  ];
}

// Load test environment configuration
try {
  const configPath = path.resolve(__dirname, `../../config/test-env.${ENV}.json`);
  const configData = fs.readFileSync(configPath, 'utf8');
  testConfig = JSON.parse(configData);
  console.log(`Loaded test environment configuration for ${ENV}`);
} catch (error) {
  console.warn(`Failed to load test environment configuration for ${ENV}. Using default values.`);
  // Default configuration if file cannot be loaded
  testConfig = {
    baseUrl: 'https://www.guidanceresources-stg.compsych-ad.int',
    credentials: {
      username: 'rayth11',
      password: 'Testtest2'
    },
    registration: {
      regUsername: 'testauto',
      regPassword: 'Test@123',
      regEmail: '<EMAIL>',
      regSecretAns: 'testanswer',
      regFirstname: 'Test',
      regLastname: 'User',
      regEmpId: 'EMP123',
      regPhoneNo: '1234567890',
      regZipcode: '12345'
    }
  };
}

/**
 * Get environment variable value from test configuration
 * @param key The environment variable key
 * @returns The environment variable value or empty string if not found
 */
export function getEnv(key: string): string {
  // Check credentials section
  if (testConfig.credentials && testConfig.credentials[key]) {
    return testConfig.credentials[key];
  }

  // Check registration section
  if (testConfig.registration && testConfig.registration[key]) {
    return testConfig.registration[key];
  }

  // Check root level
  if (testConfig[key]) {
    return testConfig[key];
  }

  // Fall back to process.env
  const value = process.env[key];
  if (!value) {
    console.warn(`Environment variable ${key} not found in test configuration or process.env`);
    return '';
  }
  return value;
}

/**
 * Login to the application using the legacy login page
 * @param page The Playwright page
 * @param username The username
 * @param password The password
 */
export async function login(page: Page, username: string, password: string): Promise<void> {
  await page.goto('/groWeb/login/login.xhtml');
  await page.locator('#login\\:userName').fill(username);
  await page.locator('#login\\:password').fill(password);
  await page.locator('#login\\:loginButton').click();
}

/**
 * Login to the application using the new Angular login page
 * @param page The Playwright page
 * @param username The username
 * @param password The password
 */
export async function loginToNgApp(page: Page, username: string, password: string): Promise<void> {
  await page.goto('/groNg/#/login');
  await page.locator('input[formcontrolname="userName"]').fill(username);
  await page.locator('input[formcontrolname="password"]').fill(password);
  await page.locator('button[type="submit"]').click();

  // Wait for navigation to complete
  await page.waitForURL('**/home');
}

/**
 * Get credential by WebID
 * @param webId The WebID to look up
 * @returns The credential object or undefined if not found
 */
export function getCredentialByWebId(webId: string): Credential | undefined {
  return testCredentials.find(cred => cred.WebID.toLowerCase() === webId.toLowerCase());
}

/**
 * Get credential by username
 * @param username The username to look up
 * @returns The credential object or undefined if not found
 */
export function getCredentialByUsername(username: string): Credential | undefined {
  return testCredentials.find(cred => cred.Username.toLowerCase() === username.toLowerCase());
}

/**
 * Login to the application using WebID
 * @param page The Playwright page
 * @param webId The WebID to use for login
 */
export async function loginWithWebId(page: Page, webId: string): Promise<void> {
  const credential = getCredentialByWebId(webId);
  if (!credential) {
    throw new Error(`No credential found for WebID: ${webId}`);
  }

  await loginToNgApp(page, credential.Username, credential.Password);
}
