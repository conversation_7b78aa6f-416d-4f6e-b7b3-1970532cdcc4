export interface RegistrationData {
    username: string;
    password: string;
    email: string;
    secretAnswer: string;
    firstName: string;
    lastName: string;
    empId: string;
    phoneNo: string;
    zipcode: string;
  }
  
  export function generateRegistrationData(): RegistrationData {
    const uniqueId = Date.now();
    return {
      username: `testuser${uniqueId}`,
      password: `P@ssword${uniqueId}`,
      email: `test${uniqueId}@example.com`,
      secretAnswer: `answer${uniqueId}`,
      firstName: `First${uniqueId}`,
      lastName: `Last${uniqueId}`,
      empId: `EMP${uniqueId}`,
      phoneNo: `123456${Math.floor(Math.random() * 10000)}`,
      zipcode: `${10000 + Math.floor(Math.random() * 90000)}`
    };
  }
  