import { Before, After, BeforeAll, AfterAll } from '@cucumber/cucumber';
import { CustomWorld } from './world';

BeforeAll(async function () {
  console.log('Starting test suite...');
});

Before({ timeout: 30000 }, async function (this: CustomWorld) {
  console.log('Initializing browser for scenario...');
  await this.init();
});

After({ timeout: 30000 }, async function (this: CustomWorld) {
  console.log('Cleaning up browser after scenario...');
  await this.cleanup();
});

AfterAll(async function () {
  console.log('Test suite completed.');
});
