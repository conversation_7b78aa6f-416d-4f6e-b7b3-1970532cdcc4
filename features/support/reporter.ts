import * as reporter from 'cucumber-html-reporter';
// @ts-ignore - No type definitions available
import * as multiReport from 'multiple-cucumber-html-reporter';
import * as fs from 'fs';
import * as path from 'path';

// Options for cucumber-html-reporter
const cucumberReporterOptions: reporter.Options = {
  theme: 'bootstrap',
  jsonFile: 'reports/cucumber-report.json',
  output: 'reports/cucumber-html-report.html',
  reportSuiteAsScenarios: true,
  scenarioTimestamp: true,
  launchReport: false,
  metadata: {
    'App Version': '1.0.0',
    'Test Environment': process.env.TEST_ENV || 'dev',
    Browser: 'Chrome, Firefox, Edge',
    Platform: 'Windows 10',
    Parallel: 'Scenarios',
    Executed: 'Local'
  }
};

// Options for multiple-cucumber-html-reporter
const multiCucumberReporterOptions = {
  jsonDir: 'reports',
  reportPath: 'reports/multi-report/',
  reportName: 'GRO E2E Test Report',
  pageTitle: 'GRO E2E Test Report',
  displayDuration: true,
  displayReportTime: true,
  metadata: {
    browser: {
      name: 'chrome',
      version: '116'
    },
    device: 'Local test machine',
    platform: {
      name: 'windows',
      version: '10'
    }
  },
  customData: {
    title: 'Run info',
    data: [
      { label: 'Project', value: 'GRO E2E Testing' },
      { label: 'Environment', value: process.env.TEST_ENV || 'dev' },
      { label: 'Execution Date', value: new Date().toISOString().slice(0, 10) }
    ]
  }
};

/**
 * Generate HTML report from JSON report
 */
export function generateReport(jsonFile: string = 'reports/cucumber-report.json'): void {
  try {
    // Update the jsonFile path
    cucumberReporterOptions.jsonFile = jsonFile;

    // Set the output file name based on the input JSON file
    const baseName = path.basename(jsonFile, '.json');
    cucumberReporterOptions.output = `reports/${baseName}-html-report.html`;

    // Generate the report
    reporter.generate(cucumberReporterOptions);

    console.log(`HTML report generated at ${cucumberReporterOptions.output}`);
  } catch (error) {
    console.error('Error generating report:', error);
  }
}

/**
 * Generate a consolidated HTML report from all JSON reports
 */
export function generateMultiReport(): void {
  try {
    // Create the report directory if it doesn't exist
    if (!fs.existsSync('reports/multi-report')) {
      fs.mkdirSync('reports/multi-report', { recursive: true });
    }

    // Generate the report
    multiReport.generate(multiCucumberReporterOptions);

    console.log(`Multi HTML report generated at ${multiCucumberReporterOptions.reportPath}`);
  } catch (error) {
    console.error('Error generating multi report:', error);
  }
}
