{"name": "my-project", "version": "1.0.0", "description": "Playwright with TypeScript and Cucumber BDD", "main": "index.js", "scripts": {"test": "cucumber-js", "test:chromium": "cucumber-js --profile chromium", "test:firefox": "cucumber-js --profile firefox", "test:webkit": "cucumber-js --profile webkit", "test:all": "npm run test:chromium && npm run test:firefox && npm run test:webkit", "test:trace": "cucumber-js --profile trace", "test:dev": "cross-env TEST_ENV=dev cucumber-js --profile chromium", "test:stg": "cross-env TEST_ENV=stg cucumber-js --profile chromium", "test:prod": "cross-env TEST_ENV=prod cucumber-js --profile chromium", "test:login": "cucumber-js --profile chromium --tags @LoginSanity", "test:registration": "cucumber-js --profile chromium --tags @RegistrationSanity", "test:sanity": "cucumber-js --profile chromium --tags @SanityTest", "test:smoke": "cucumber-js --profile chromium --tags @Smoke", "test:regression": "cucumber-js --profile chromium --tags @Regression", "test:wip": "cucumber-js --profile chromium --tags @WIP", "test:multi-account": "cucumber-js --profile chromium --tags @LoginMultiAccount", "test:parallel": "cucumber-js --profile parallel", "test:parallel:chromium": "cucumber-js --profile parallel-chromium", "test:parallel:firefox": "cucumber-js --profile parallel-firefox", "test:parallel:webkit": "cucumber-js --profile parallel-webkit", "test:parallel:all": "npm run test:parallel:chromium && npm run test:parallel:firefox && npm run test:parallel:webkit", "test:parallel:sanity": "cucumber-js --profile parallel-chromium --tags @SanityTest", "test:parallel:smoke": "cucumber-js --profile parallel-chromium --tags @Smoke", "test:parallel:regression": "cucumber-js --profile parallel-chromium --tags @Regression", "show-report": "npx playwright show-report playwright-report", "report": "ts-node generate-reports.ts", "test:report": "npm test && npm run report", "test:chromium:report": "npm run test:chromium && npm run report", "test:parallel:report": "npm run test:parallel && npm run report", "open:report": "start reports/cucumber-report-html-report.html", "open:multi-report": "start reports/multi-report/index.html", "ui": "npx playwright test --ui --config=playwright-ui.config.ts", "ui:chromium": "npx playwright test --ui --project=chromium --config=playwright-ui.config.ts", "ui:firefox": "npx playwright test --ui --project=firefox --config=playwright-ui.config.ts", "ui:webkit": "npx playwright test --ui --project=webkit --config=playwright-ui.config.ts", "ui:debug": "npx playwright test --debug --config=playwright-ui.config.ts", "ui:gropr1": "cross-env TEST_ENV=prod npx playwright test ui-mode-tests/gropr1-smoke-regression.spec.ts --config=playwright-ui.config.ts", "ui:gropr1:all-browsers": "cross-env TEST_ENV=prod npx playwright test ui-mode-tests/gropr1-smoke-regression.spec.ts --config=playwright-ui.config.ts --project=chromium --project=firefox --project=webkit", "allure:generate": "npx allure generate allure-results --clean -o allure-report", "allure:open": "npx allure open allure-report", "allure:serve": "npx allure serve allure-results", "test:gropr1:allure": "npm run ui:gropr1:all-browsers && npm run allure:generate && npm run allure:open", "test:smoke-regression:all-browsers": "cross-env TEST_ENV=prod npx cucumber-js --profile parallel-chromium --tags \"@Smoke and @Regression and @prod\" && cross-env TEST_ENV=prod npx cucumber-js --profile parallel-firefox --tags \"@Smoke and @Regression and @prod\" && cross-env TEST_ENV=prod npx cucumber-js --profile parallel-webkit --tags \"@Smoke and @Regression and @prod\"", "test:login-homepage:allure": "npm run test:smoke-regression:all-browsers && npm run allure:generate && npm run allure:open"}, "keywords": ["playwright", "cucumber", "bdd", "testing", "e2e"], "author": "", "license": "ISC", "devDependencies": {"@cucumber/cucumber": "^9.1.2", "@playwright/test": "^1.38.0", "@types/cucumber": "^7.0.0", "@types/node": "^20.5.9", "allure-commandline": "^2.25.0", "allure-playwright": "^2.10.0", "cross-env": "^7.0.3", "cucumber-html-reporter": "^6.0.0", "multiple-cucumber-html-reporter": "^3.4.0", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "dependencies": {"my-project": "file:"}}