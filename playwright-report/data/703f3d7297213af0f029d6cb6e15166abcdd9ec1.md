# Test info

- Name: Login Test - GROPR1 Account >> User can login with second account (rayth11)
- Location: /Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/ui-mode-tests/login.spec.ts:83:7

# Error details

```
TimeoutError: locator.fill: Timeout 10000ms exceeded.
Call log:
  - waiting for locator('input[formcontrolname="username"]')

    at /Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/ui-mode-tests/login.spec.ts:95:61
```

# Page snapshot

```yaml
- main:
  - heading "Welcome To ComPsych GuidanceResources®" [level=1]:
    - text: Welcome To ComPsych GuidanceResources
    - superscript: ®
  - button "USA English USA - English":
    - img "USA English"
    - text: USA - English
  - text: Username
  - button "Help"
  - textbox "Username"
  - text: Password
  - textbox "Password"
  - checkbox "Remember Me"
  - text: Remember Me
  - button "Forgot Username?"
  - text: "|"
  - button "Forgot Password?"
  - text: To access, alter, or erase personal data, go to
  - emphasis: My Profile.
  - button "Login"
  - button "Register"
  - text: In order to provide a better experience, GuidanceResources Online uses cookies. By continuing to use this site you are agreeing to our
  - link "use of cookies.":
    - /url: "#/utilityNavigation/privacy/online-privacy"
  - text: . I agree. x
- contentinfo:
  - link "Get it on Google Play":
    - /url: https://play.google.com/store/apps/details?id=com.compsych.gro
    - img "Get it on Google Play"
  - link "apple appstore logo":
    - /url: https://apps.apple.com/us/app/guidancenow/id556008763
    - img "apple appstore logo"
  - text: Copyright © 2025 ComPsych® Corporation. All rights reserved. Privacy and Terms of Use
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | // Simple login test for GROPR1 account in production environment
   4 |
   5 | test.describe('Login Test - GROPR1 Account', () => {
   6 |   test('Debug: Inspect login page', async ({ page }) => {
   7 |     // Navigate to login page
   8 |     await page.goto('/groNg/#/login');
   9 |
   10 |     // Wait for page to load
   11 |     await page.waitForLoadState('networkidle');
   12 |
   13 |     // Wait a bit more for any dynamic content
   14 |     await page.waitForTimeout(3000);
   15 |
   16 |     // Take a screenshot to see what's on the page
   17 |     await page.screenshot({ path: 'debug-login-page.png', fullPage: true });
   18 |
   19 |     // Log the page title
   20 |     const title = await page.title();
   21 |     console.log('Page title:', title);
   22 |
   23 |     // Log the current URL
   24 |     console.log('Current URL:', page.url());
   25 |
   26 |     // Check if there are any input fields
   27 |     const inputs = await page.locator('input').count();
   28 |     console.log('Number of input fields found:', inputs);
   29 |
   30 |     // Get all input elements and their attributes
   31 |     const inputElements = await page.locator('input').all();
   32 |     for (let i = 0; i < inputElements.length; i++) {
   33 |       const input = inputElements[i];
   34 |       const type = await input.getAttribute('type');
   35 |       const name = await input.getAttribute('name');
   36 |       const id = await input.getAttribute('id');
   37 |       const formControlName = await input.getAttribute('formcontrolname');
   38 |       const placeholder = await input.getAttribute('placeholder');
   39 |       const className = await input.getAttribute('class');
   40 |       console.log(`Input ${i}: type="${type}", name="${name}", id="${id}", formcontrolname="${formControlName}", placeholder="${placeholder}", class="${className}"`);
   41 |     }
   42 |
   43 |     // Also check for any forms
   44 |     const forms = await page.locator('form').count();
   45 |     console.log('Number of forms found:', forms);
   46 |
   47 |     // Check page content
   48 |     const bodyText = await page.locator('body').textContent();
   49 |     console.log('Page contains login text:', bodyText?.toLowerCase().includes('login'));
   50 |     console.log('Page contains username text:', bodyText?.toLowerCase().includes('username'));
   51 |     console.log('Page contains password text:', bodyText?.toLowerCase().includes('password'));
   52 |   });
   53 |
   54 |   test('GROPR1 can login successfully', async ({ page }) => {
   55 |     // Navigate to login page
   56 |     await page.goto('/groNg/#/login');
   57 |
   58 |     // Wait for page to load
   59 |     await page.waitForLoadState('networkidle');
   60 |
   61 |     // Use GROPR1 credentials
   62 |     const username = 'GROPR1';
   63 |     const password = 'Testtest1';
   64 |
   65 |     // Fill in login form with increased timeout
   66 |     await page.locator('input[formcontrolname="userName"]').fill(username, { timeout: 10000 });
   67 |     await page.locator('input[formcontrolname="password"]').fill(password, { timeout: 10000 });
   68 |
   69 |     // Click login button
   70 |     await page.locator('button[type="submit"]').click({ timeout: 10000 });
   71 |
   72 |     // Wait for navigation to complete with increased timeout
   73 |     await page.waitForURL('**/home', { timeout: 30000 });
   74 |
   75 |     // Check if user is logged in by looking for a common element on the home page
   76 |     const userMenuElement = page.locator('.navMenu');
   77 |     await expect(userMenuElement).toBeVisible({ timeout: 15000 });
   78 |
   79 |     // Verify we're on the home page
   80 |     await expect(page).toHaveURL(/groNg\/#\/home/<USER>
   81 |   });
   82 |
   83 |   test('User can login with second account (rayth11)', async ({ page }) => {
   84 |     // Navigate to login page
   85 |     await page.goto('/groNg/#/login');
   86 |
   87 |     // Use production credentials
   88 |     const username = 'rayth11';
   89 |     const password = 'Testtestp';
   90 |
   91 |     // Wait for page to load
   92 |     await page.waitForLoadState('networkidle');
   93 |
   94 |     // Fill in login form with increased timeout
>  95 |     await page.locator('input[formcontrolname="username"]').fill(username, { timeout: 10000 });
      |                                                             ^ TimeoutError: locator.fill: Timeout 10000ms exceeded.
   96 |     await page.locator('input[formcontrolname="password"]').fill(password, { timeout: 10000 });
   97 |
   98 |     // Click login button
   99 |     await page.locator('button[type="submit"]').click({ timeout: 10000 });
  100 |
  101 |     // Wait for navigation to complete with increased timeout
  102 |     await page.waitForURL('**/home', { timeout: 30000 });
  103 |
  104 |     // Check if user is logged in by looking for a common element on the home page
  105 |     const userMenuElement = page.locator('.navMenu');
  106 |     await expect(userMenuElement).toBeVisible({ timeout: 15000 });
  107 |
  108 |     // Verify we're on the home page
  109 |     await expect(page).toHaveURL(/groNg\/#\/home/<USER>
  110 |
  111 |     // Check for a specific element on the home page
  112 |     const headerElement = page.locator('h2[class=ng-star-inserted]');
  113 |     await expect(headerElement).toHaveText('Additional Tools & Resources', { timeout: 15000 });
  114 |   });
  115 |
  116 |   test('User can login with third account (produser3)', async ({ page }) => {
  117 |     // Navigate to login page
  118 |     await page.goto('/groNg/#/login');
  119 |
  120 |     // Use production credentials
  121 |     const username = 'produser3';
  122 |     const password = 'ProdTest3';
  123 |
  124 |     // Wait for page to load
  125 |     await page.waitForLoadState('networkidle');
  126 |
  127 |     // Fill in login form with increased timeout
  128 |     await page.locator('input[formcontrolname="username"]').fill(username, { timeout: 10000 });
  129 |     await page.locator('input[formcontrolname="password"]').fill(password, { timeout: 10000 });
  130 |
  131 |     // Click login button
  132 |     await page.locator('button[type="submit"]').click({ timeout: 10000 });
  133 |
  134 |     // Wait for navigation to complete with increased timeout
  135 |     await page.waitForURL('**/home', { timeout: 30000 });
  136 |
  137 |     // Check if user is logged in by looking for a common element on the home page
  138 |     const userMenuElement = page.locator('.navMenu');
  139 |     await expect(userMenuElement).toBeVisible({ timeout: 15000 });
  140 |
  141 |     // Verify we're on the home page
  142 |     await expect(page).toHaveURL(/groNg\/#\/home/<USER>
  143 |
  144 |     // Check for a specific element on the home page
  145 |     const headerElement = page.locator('h2[class=ng-star-inserted]');
  146 |     await expect(headerElement).toHaveText('Additional Tools & Resources', { timeout: 15000 });
  147 |   });
  148 |   
  149 |   test('User cannot login with invalid credentials', async ({ page }) => {
  150 |     // Navigate to login page
  151 |     await page.goto('/groNg/#/login');
  152 |     
  153 |     // Use invalid credentials
  154 |     await page.locator('input[formcontrolname="username"]').fill('invalid_user');
  155 |     await page.locator('input[formcontrolname="password"]').fill('invalid_password');
  156 |     
  157 |     // Click login button
  158 |     await page.locator('button[type="submit"]').click();
  159 |     
  160 |     // Check for error message
  161 |     const errorMessage = page.locator('.error-message');
  162 |     await expect(errorMessage).toBeVisible();
  163 |     await expect(errorMessage).toContainText(/invalid|failed|incorrect/i);
  164 |   });
  165 | });
  166 |
```