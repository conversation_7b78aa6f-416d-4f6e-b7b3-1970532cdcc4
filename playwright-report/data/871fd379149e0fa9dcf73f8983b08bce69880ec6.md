# Test info

- Name: Multi-Account Login Tests >> User can login with WebID: RTX
- Location: /Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/ui-mode-tests/multi-account-login.spec.ts:11:9

# Error details

```
Error: locator.fill: Test ended.
Call log:
  - waiting for locator('input[formcontrolname="username"]')

    at /Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/ui-mode-tests/multi-account-login.spec.ts:24:63
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 | import { getCredentialByWebId } from '../features/support/helpers';
   3 |
   4 | // This file contains Playwright tests that can be run in UI mode
   5 | // These tests mirror the functionality in the Cucumber features
   6 |
   7 | test.describe('Multi-Account Login Tests', () => {
   8 |   const webIds = ['RTX', 'pnc', 'aon', 'teconn', 'MacysIncEAP'];
   9 |   
  10 |   for (const webId of webIds) {
  11 |     test(`User can login with WebID: ${webId}`, async ({ page }) => {
  12 |       // Get credentials for this WebID
  13 |       const credential = getCredentialByWebId(webId);
  14 |       
  15 |       if (!credential) {
  16 |         test.skip(`No credentials found for WebID: ${webId}`);
  17 |         return;
  18 |       }
  19 |       
  20 |       // Navigate to login page
  21 |       await page.goto('/groNg/#/login');
  22 |       
  23 |       // Fill in login form
> 24 |       await page.locator('input[formcontrolname="username"]').fill(credential.Username);
     |                                                               ^ Error: locator.fill: Test ended.
  25 |       await page.locator('input[formcontrolname="password"]').fill(credential.Password);
  26 |       
  27 |       // Click login button
  28 |       await page.locator('button[type="submit"]').click();
  29 |       
  30 |       // Wait for navigation to complete
  31 |       await page.waitForURL('**/home');
  32 |       
  33 |       // Check if user is logged in by looking for a common element on the home page
  34 |       const userMenuElement = page.locator('.navMenu');
  35 |       await expect(userMenuElement).toBeVisible();
  36 |       
  37 |       // Verify we're on the home page
  38 |       await expect(page).toHaveURL(new RegExp('groNg/#/home'));
  39 |       
  40 |       // Check for a specific element on the home page
  41 |       const headerElement = page.locator('h2[class=ng-star-inserted]');
  42 |       await expect(headerElement).toHaveText('Additional Tools & Resources');
  43 |     });
  44 |   }
  45 | });
  46 |
```