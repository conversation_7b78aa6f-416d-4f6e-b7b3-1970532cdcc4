# Test info

- Name: Login Test - GROPR1 Account >> User cannot login with invalid credentials
- Location: /Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/ui-mode-tests/login.spec.ts:149:7

# Error details

```
Error: locator.fill: Target page, context or browser has been closed
Call log:
  - waiting for locator('input[formcontrolname="username"]')

    at /Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/ui-mode-tests/login.spec.ts:154:61
```

# Test source

```ts
   54 |   test('GROPR1 can login successfully', async ({ page }) => {
   55 |     // Navigate to login page
   56 |     await page.goto('/groNg/#/login');
   57 |
   58 |     // Wait for page to load
   59 |     await page.waitForLoadState('networkidle');
   60 |
   61 |     // Use GROPR1 credentials
   62 |     const username = 'GROPR1';
   63 |     const password = 'Testtest1';
   64 |
   65 |     // Fill in login form with increased timeout
   66 |     await page.locator('input[formcontrolname="userName"]').fill(username, { timeout: 10000 });
   67 |     await page.locator('input[formcontrolname="password"]').fill(password, { timeout: 10000 });
   68 |
   69 |     // Click login button
   70 |     await page.locator('button[type="submit"]').click({ timeout: 10000 });
   71 |
   72 |     // Wait for navigation to complete with increased timeout
   73 |     await page.waitForURL('**/home', { timeout: 30000 });
   74 |
   75 |     // Check if user is logged in by looking for a common element on the home page
   76 |     const userMenuElement = page.locator('.navMenu');
   77 |     await expect(userMenuElement).toBeVisible({ timeout: 15000 });
   78 |
   79 |     // Verify we're on the home page
   80 |     await expect(page).toHaveURL(/groNg\/#\/home/<USER>
   81 |   });
   82 |
   83 |   test('User can login with second account (rayth11)', async ({ page }) => {
   84 |     // Navigate to login page
   85 |     await page.goto('/groNg/#/login');
   86 |
   87 |     // Use production credentials
   88 |     const username = 'rayth11';
   89 |     const password = 'Testtestp';
   90 |
   91 |     // Wait for page to load
   92 |     await page.waitForLoadState('networkidle');
   93 |
   94 |     // Fill in login form with increased timeout
   95 |     await page.locator('input[formcontrolname="username"]').fill(username, { timeout: 10000 });
   96 |     await page.locator('input[formcontrolname="password"]').fill(password, { timeout: 10000 });
   97 |
   98 |     // Click login button
   99 |     await page.locator('button[type="submit"]').click({ timeout: 10000 });
  100 |
  101 |     // Wait for navigation to complete with increased timeout
  102 |     await page.waitForURL('**/home', { timeout: 30000 });
  103 |
  104 |     // Check if user is logged in by looking for a common element on the home page
  105 |     const userMenuElement = page.locator('.navMenu');
  106 |     await expect(userMenuElement).toBeVisible({ timeout: 15000 });
  107 |
  108 |     // Verify we're on the home page
  109 |     await expect(page).toHaveURL(/groNg\/#\/home/<USER>
  110 |
  111 |     // Check for a specific element on the home page
  112 |     const headerElement = page.locator('h2[class=ng-star-inserted]');
  113 |     await expect(headerElement).toHaveText('Additional Tools & Resources', { timeout: 15000 });
  114 |   });
  115 |
  116 |   test('User can login with third account (produser3)', async ({ page }) => {
  117 |     // Navigate to login page
  118 |     await page.goto('/groNg/#/login');
  119 |
  120 |     // Use production credentials
  121 |     const username = 'produser3';
  122 |     const password = 'ProdTest3';
  123 |
  124 |     // Wait for page to load
  125 |     await page.waitForLoadState('networkidle');
  126 |
  127 |     // Fill in login form with increased timeout
  128 |     await page.locator('input[formcontrolname="username"]').fill(username, { timeout: 10000 });
  129 |     await page.locator('input[formcontrolname="password"]').fill(password, { timeout: 10000 });
  130 |
  131 |     // Click login button
  132 |     await page.locator('button[type="submit"]').click({ timeout: 10000 });
  133 |
  134 |     // Wait for navigation to complete with increased timeout
  135 |     await page.waitForURL('**/home', { timeout: 30000 });
  136 |
  137 |     // Check if user is logged in by looking for a common element on the home page
  138 |     const userMenuElement = page.locator('.navMenu');
  139 |     await expect(userMenuElement).toBeVisible({ timeout: 15000 });
  140 |
  141 |     // Verify we're on the home page
  142 |     await expect(page).toHaveURL(/groNg\/#\/home/<USER>
  143 |
  144 |     // Check for a specific element on the home page
  145 |     const headerElement = page.locator('h2[class=ng-star-inserted]');
  146 |     await expect(headerElement).toHaveText('Additional Tools & Resources', { timeout: 15000 });
  147 |   });
  148 |   
  149 |   test('User cannot login with invalid credentials', async ({ page }) => {
  150 |     // Navigate to login page
  151 |     await page.goto('/groNg/#/login');
  152 |     
  153 |     // Use invalid credentials
> 154 |     await page.locator('input[formcontrolname="username"]').fill('invalid_user');
      |                                                             ^ Error: locator.fill: Target page, context or browser has been closed
  155 |     await page.locator('input[formcontrolname="password"]').fill('invalid_password');
  156 |     
  157 |     // Click login button
  158 |     await page.locator('button[type="submit"]').click();
  159 |     
  160 |     // Check for error message
  161 |     const errorMessage = page.locator('.error-message');
  162 |     await expect(errorMessage).toBeVisible();
  163 |     await expect(errorMessage).toContainText(/invalid|failed|incorrect/i);
  164 |   });
  165 | });
  166 |
```