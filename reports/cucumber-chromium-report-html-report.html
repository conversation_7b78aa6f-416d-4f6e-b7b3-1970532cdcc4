<!DOCTYPE html>
<html>

<head>
  <title>Cucumber Feature Report</title>
  <link rel="icon" href="https://www.lambdatest.com/favicon.ico">
  <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
  <style type="text/css">
    .panel-heading {
    padding: 0;
}

@media (min-width: 768px) {
    .pull-right-lg {
        float: left;
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .pull-right-lg {
        float: right;
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .pull-right-lg {
        float: right;
    }
}

@media (min-width: 1200px) {
    .pull-right-lg {
        float: right;
    }
}

.left {
    float: left;
}

.metadata {
    overflow: auto;
    letter-spacing: 0.2px;
    border-color: white;
    line-height: 1.6;
    color: #4d4d4d;
    font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif;
    font-size: 13px;
    padding-bottom: 3px;
}

.panel-heading a {
    padding: 10px 15px;
    display: block;
    position: relative;
    text-decoration: none;
}

.panel-heading i.glyphicon-chevron-down {
    display: none;
}

.panel-heading i.glyphicon-chevron-right {
    display: inline-block;
}

.panel-heading.open i.glyphicon-chevron-down {
    display: inline-block;
}

.panel-heading.open i.glyphicon-chevron-right {
    display: none;
}

.generated-on {
    text-align: right;
    padding-bottom: 10px;
}

.panel-title b {
    padding-right: 10px;
}

.panel-heading .label-container {
    position: absolute;
    top: 8px;
    right: 8px;
}

.panel-heading .label-container label {
    margin-left: 5px;
    padding: 5px;
}

.navbar .label-container {
    position: absolute;
    right: 10px;
    top: 14px;
}

.navbar {
    margin-bottom: 10px;
}

.navbar .label {
    font-size: 20px;
}

.navbar .project-name {
    position: absolute;
    top: 10px;
    left: 50%;
    margin-left: -100px;
    text-align: center;
    font-size: 20px;
    font-weight: bold;
}

.tags {
    margin-left: 18px;
    margin-right: 20px;
    padding-top: 5px;
    margin-bottom: -4px;
}

.tag {
    font-size: 13px;
    color: #696969;
    letter-spacing: 0.3px;
    font-weight: bold;
    font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif;
}

.chart {
    padding-bottom: 15px;
}

div.chart div div svg rect {
    fill: #f5f5f5;
}

.arguments {
    margin-left: 30px;
    margin-top: 1em;
    margin-bottom: 15px;
}

#directory {
    background-color: #f0f0f0;
}

.screenshot {
    padding: 2% 0 2% 0;
}

.description {
    background-color: white;
    border-color: white;
    line-height: 1.6;
    color: #6f6f6f;
    font-weight: 400;
    font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif;
    font-size: 14px;
    padding: 0.1em 0.5em 1.2em 1.5em
}

#scenario-description {
    padding-bottom: 1em;
    padding-left: 0.2em;
}

.scrollBar {
    overflow-x: scroll;
}

table {
    border-collapse: collapse;
}

table, th, td {
    border: 1px solid black;
}

th, td {
    text-align: left;
    padding: 8px;
}

th {
    background-color: #f5f5f5;
    color: black;
}

.info {
    background-color: #fbfbfb;
}

pre {
    display: block;
    padding: 10px;
    margin-top: 1em;
    margin-right: 3em;
    font-size: 13px;
    line-height: 1.42857143;
    word-break: break-all;
    word-wrap: break-word;
    color: #333;
    background-color: #f5f5f5;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.step-duration {
    float: right;
    color: #BDBDBD;
}

.footer-div {
    text-align: right;
    vertical-align: middle;
    height: 3.5%;
    width: 100%;
}

.footer-container {
    margin-right: 10px;
    margin-top: 5px;
    width: 18%;
    z-index: 10;
    position: absolute;
    right: 0;
    bottom: 10px;
    text-align: center;
    background-color: transparent;
}

.show-modal:hover {
    background-color: #f0f0f0;
}

.footer-link {
    font-size: 13px;
    float: right;
}

.footer-link:hover {
    color: darkgray;
}

.steps {
    padding-left: 10px;
    padding-right: 10px;
    margin-bottom: -4px;
}

.all-features {
    padding-top: 0.6em;
}

.keyword {
    color: #616161;
}

.stepname {
    word-wrap: break-word;
}

.feature-passed {
    display: block;
}

.feature-failed{
    display: block;
}

.label-saffron{
    background-color: #ff9933;
}

.label-retries {
    display: flex;
    flex-wrap: wrap;
    width: 90px;
    justify-content: flex-end;
    gap: 5px;
}

.label-retries > span:first-child {
    min-width: 100%;
}

.ellipsis {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: all .2s linear;
    white-space: nowrap;
    padding:.5rem 1rem;
}
.ellipsis:focus, .ellipsis:hover {
  color:transparent;
}
.ellipsis:focus:after,.ellipsis:hover:after{
    content:attr(data-text);
    overflow: visible;
    text-overflow: inherit;
    background: #fff;
    position: absolute;
    left:auto;
    top:auto;
    width: auto;
    max-width: 100%;
    border: 1px solid #eaebec;
    padding: 0 .5rem;
    box-shadow: 0 2px 4px 0 rgba(0,0,0,.28);
    white-space: normal;
    word-wrap: break-word;
    display:block;
    color:black;
    margin-top:-1.25rem;
    z-index: 1;
  }

    /** Issue in google-charts tooltip - https://github.com/google/google-visualization-issues/issues/2162 */
    .google-visualization-tooltip {
      pointer-events: none;
    }

    .count-wrapper {
      display: flex;
      gap: 15px;
    }

    .card {
      border: 1px solid #f5f5f5;
      padding: 15px;
      color: #fff;
      border-radius: 5px;
      flex: 1;
    }

    .generated-on {
      margin-right: 10px;
    }

    .card h4 {
      font-size: 15px;
    }

    .red-box {
      background: #d9534e;
    }

    .blue-box {
      background: #0074ff;
    }

    .yellow-box {
      background: #f0ad4e;
    }

    .saffron-box {
      background: #ff9933;
    }

    .green-box {
      background: #5cb85b;
    }

    .gray-box {
      background: #999;
    }

    .amb-box {
      background: #218bcf;
    }

    .undefined-box {
      background: #22c2e2;
    }

    .mb-20px {
      margin-bottom: 20px;
    }
  </style>
  <meta charset="UTF-8">
</head>

<body>

  <div class="navbar navbar-default navbar-static-top" role="navigation">
    <div class="container">
      <div class="navbar-header">
        
          <a class="navbar-brand">
            Cucumberjs Report
          </a>
          <div class="project-name visible-md visible-lg">
            my-project
          </div>
          <div class="label-container">
            <div class="generated-on">
              Thu May 29 2025 14:44:08 GMT-0500 (Central Daylight Time)
            </div>
          </div>
      </div>
    </div>
  </div>

  <div class="container">

    <div class="count-wrapper mb-20px">
      <div class="card blue-box">
        <div class="feature-title">
          <h4>All Scenarios</h2>
        </div>
        <div class="feature-value">
          <h5>
            1
          </h5>
        </div>
      </div>
      <div class="card green-box">
        <div class="feature-title">
          <h4>Passed Scenarios</h2>
        </div>
        <div class="feature-value">
          <h5>
            0
          </h5>
        </div>
      </div>
      <div class="card red-box">
        <div class="feature-title">
          <h4>Failed Scenarios</h2>
        </div>
        <div class="feature-value">
          <h5>
            0
          </h5>
        </div>
      </div>
      
          
            <div class="card yellow-box">
              <div class="feature-title">
                <h4>Skipped Scenarios</h2>
              </div>
              <div class="feature-value">
                <h5>
                  1
                </h5>
              </div>
            </div>
            
              
                  
                      
    </div>
    <div class="row">
      <div class="chart col-lg-6 col-md-6" id="piechart_features"></div>
      <div class="chart col-lg-6 col-md-6" id="piechart_scenarios"></div>
    </div>

    <div>
      <p class="text-left">
        Execution Time: < 1ms
      </p>
      <p class="text-right">
        <a id="expand_all">Expand All</a>
        <span> | </span>
        <a id="collapse_all">Collapse All</a>
      </p>
    </div>

    
      <div class="panel panel-default">
        <div class="panel-heading open">
          <h4 class="panel-title">
            <a data-toggle="collapse" href="#logOutput">
              <i class="glyphicon glyphicon-chevron-right"></i>
              <i class="glyphicon glyphicon-chevron-down"></i>
              <b>Metadata</b>
            </a>
          </h4>
        </div>
        <div id="logOutput" class="panel-collapse collapse in">
          <div class="panel-body">
            <div class="row">
              
                
                  
                    
                      <div class="clearfix metadata col-xs-12 col-sm-6 col-md-6 col-lg-6">
                        <div class=pull-left>
                          <strong>
                            App Version:
                          </strong>
                          1.0.0
                        </div>
                      </div>
                      
                  
                    
                      <div class="clearfix metadata col-xs-12 col-sm-6 col-md-6 col-lg-6">
                        <div class=pull-right-lg>
                          <strong>
                            Test Environment:
                          </strong>
                          dev
                        </div>
                      </div>
                      
                  
                    
                      <div class="clearfix metadata col-xs-12 col-sm-6 col-md-6 col-lg-6">
                        <div class=pull-left>
                          <strong>
                            Browser:
                          </strong>
                          Chrome, Firefox, Edge
                        </div>
                      </div>
                      
                  
                    
                      <div class="clearfix metadata col-xs-12 col-sm-6 col-md-6 col-lg-6">
                        <div class=pull-right-lg>
                          <strong>
                            Platform:
                          </strong>
                          Windows 10
                        </div>
                      </div>
                      
                  
                    
                      <div class="clearfix metadata col-xs-12 col-sm-6 col-md-6 col-lg-6">
                        <div class=pull-left>
                          <strong>
                            Parallel:
                          </strong>
                          Scenarios
                        </div>
                      </div>
                      
                  
                    
                      <div class="clearfix metadata col-xs-12 col-sm-6 col-md-6 col-lg-6">
                        <div class=pull-right-lg>
                          <strong>
                            Executed:
                          </strong>
                          Local
                        </div>
                      </div>
                      
            </div>
          </div>
        </div>
      </div>
      

        


<div class="row" xmlns="http://www.w3.org/1999/html">


<div class="feature-passed">

    <div class="col-lg-6 col-md-6">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    
                    <div class="tags">
                    </div>
                    
                    <a data-toggle="collapse" href="#collapseFeaturemy_project55757f10-46a9-4748-8d31-84977e80a9f7">
                        <i class="glyphicon glyphicon-chevron-right"></i>
                        <i class="glyphicon glyphicon-chevron-down"></i>
                        <b>Feature:</b>Login to GRO
                        <span class="label-container">

                        

                        <span>< 1ms</span>
                                
              
              
              
              
              <span class="label label-warning"
                                                          title="1 Scenarios Skipped">1</span>
              
                        </span>
                    </a>
                </h4>
            </div>
            <div id="collapseFeaturemy_project55757f10-46a9-4748-8d31-84977e80a9f7" class="panel-collapse collapse">
                <div class="panel-body">
                    
                    
                    
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                
                                
                                <div class="tags"> <span class="tag"> @LoginSanity</span>
                                     <span class="tag"> @SanityTest</span>
                                     <span class="tag"> @Smoke</span>
                                    
                                </div>
                                
                                <a data-toggle="collapse" href="#collapseScenariomy_projectbfcae424-f308-4820-b832-63dc04a03992">
                                    <div>

                                        <div style="padding-right: 30px">
                                            <i class="glyphicon glyphicon-chevron-right"></i>
                                            <i class="glyphicon glyphicon-chevron-down"></i>
                                            <b>Scenario:</b><div class="ellipsis" data-text="User logs in to GRO application">User logs in to GRO application</div>
                                        </div>
                                        <div>
                            <span class="label-container ">
                                <span>< 1ms</span>
                              
                              
                              
                              
                              <span class="label label-warning"
                                                                title="4 Steps Skipped">4</span>
                              
                              
                            </span>
                                        </div>
                                    </div>
                                    <div><small><i></i></small></div>
                                </a>
                            </h4>
                        </div>
                        <div id="collapseScenariomy_projectbfcae424-f308-4820-b832-63dc04a03992"
                             class="panel-collapse collapse">
                            <div class="panel-body">
                                <div></div>
                                
                                
                                
                                
                                
                                <p class="scenario-container">
                                <div class="row steps">
                                    
                                    
                                    <span class="label label-warning" title="Skipped"><i
                                            class="glyphicon glyphicon-minus"></i></span>
                                    
                                    
                                    <span class="text">
                        <span class="keyword highlight">Given </span>
                          <span class="stepname"> access to GRO login page &quot;/groNg/#/login&quot;</span>
                            
                          
                              <span class="step-duration">
                               < 1ms
                              </span>
                            
                         
                            
                          
                          <!-- Adding data table data-->
                          
                      </span>
                                
                                

                                    

                                    

                                    

                                    
                                </div>
                                </p>
                                
                                
                                
                                <p class="scenario-container">
                                <div class="row steps">
                                    
                                    
                                    <span class="label label-warning" title="Skipped"><i
                                            class="glyphicon glyphicon-minus"></i></span>
                                    
                                    
                                    <span class="text">
                        <span class="keyword highlight">When </span>
                          <span class="stepname"> user enters valid credentials</span>
                            
                          
                              <span class="step-duration">
                               < 1ms
                              </span>
                            
                         
                            
                          
                          <!-- Adding data table data-->
                          
                      </span>
                                
                                

                                    

                                    

                                    

                                    
                                </div>
                                </p>
                                
                                
                                
                                <p class="scenario-container">
                                <div class="row steps">
                                    
                                    
                                    <span class="label label-warning" title="Skipped"><i
                                            class="glyphicon glyphicon-minus"></i></span>
                                    
                                    
                                    <span class="text">
                        <span class="keyword highlight">Then </span>
                          <span class="stepname"> user should be logged in successfully</span>
                            
                          
                              <span class="step-duration">
                               < 1ms
                              </span>
                            
                         
                            
                          
                          <!-- Adding data table data-->
                          
                      </span>
                                
                                

                                    

                                    

                                    

                                    
                                </div>
                                </p>
                                
                                
                                
                                <p class="scenario-container">
                                <div class="row steps">
                                    
                                    
                                    <span class="label label-warning" title="Skipped"><i
                                            class="glyphicon glyphicon-minus"></i></span>
                                    
                                    
                                    <span class="text">
                        <span class="keyword highlight">And </span>
                          <span class="stepname"> user should see the home page</span>
                            
                          
                              <span class="step-duration">
                               < 1ms
                              </span>
                            
                         
                            
                          
                          <!-- Adding data table data-->
                          
                      </span>
                                
                                

                                    

                                    

                                    

                                    
                                </div>
                                </p>
                                
                                
                                
                                
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
    
</div>

</div>



          
  </div>



  <script src="https://code.jquery.com/jquery-1.12.4.min.js"
    integrity="sha384-nvAa0+6Qg9clwYCGGPpDQLVpLNn0fRaROjHqs13t4Ggj3Ez50XnGQqc/r8MhnRDZ"
    crossorigin="anonymous"></script>
  <script src="https://stackpath.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"
    integrity="sha384-aJ21OjlMXNL5UyIl/XNwTMqvzeRMZH2w8c5cRVpzpU8Y5bApTppSuUkhZXN0VxHd"
    crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.5.1/moment.min.js"></script>
  <script type="text/javascript" src="https://www.google.com/jsapi"></script>
  <script type="text/javascript">
    google.load("visualization", "1", { packages: ["corechart"] });
    google.setOnLoadCallback(function () {
      drawChart({
        "title": "Features",
        "failed": 0,
        "passed": 0,
        "notdefined": 0,
        "pending": 0,
        "skipped": 1,
        "ambiguous": 0
                                                        })
    });
    google.setOnLoadCallback(function () {
      drawChart({
        "title": "Scenarios",
        "failed": 0,
        "passed": 0,
        "rerun": 0,
        "notdefined": 0,
        "pending": 0,
        "skipped": 1,
        "ambiguous": 0
                                                        })
    });
  </script>
  <script>
      $(document).ready(function () {
  $('.collapse')
    .on('hide.bs.collapse', function (e) {
      e.stopPropagation();
      $(this).prev().removeClass('open');
    })
    .on('show.bs.collapse', function (e) {
      e.stopPropagation();
      $(this).prev().addClass('open');
    });

  let $generated = $('.generated-on');

  let timestamp = $generated.text();
  $generated.text('Report generated ' + moment(timestamp).fromNow());
  $generated.prop('title', new Date(timestamp).toISOString());
});

$(document).ready(function () {
  const $collapsibleItems = $('[data-toggle="collapse"]');
  const $collapseAllBtn = $('#collapse_all');
  const $expandAllBtn = $('#expand_all');

  const collapseAll = function () {
    $collapsibleItems.each(function () {
      $($(this).attr('href')).collapse('hide');
    });
  };

  const expandAll = function () {
    $collapsibleItems.each(function () {
      $($(this).attr('href')).collapse('show');
    });
  };

  $collapseAllBtn.on('click', collapseAll);
  $expandAllBtn.on('click', expandAll);

});

function toggle(className) {
  let x = $(className);
  if (x.css('display') === 'none') {
    x.css('display', 'block');
  } else {
    x.css('display', 'none');
  }
}

      function drawChart(chartData) {
  let data = google.visualization.arrayToDataTable([
    ['Task', 'Cucumber Results'],
    ['Passed', chartData.passed],
    ['Failed', chartData.failed],
    ['Pending', chartData.pending],
    ['Undefined', chartData.notdefined],
    ['Ambiguous', chartData.ambiguous],
    ['Skipped', chartData.skipped],
    ['Re-run', chartData.rerun],
  ]);

  let total =
    chartData.passed +
      chartData.failed +
      (chartData.pending || 0) +
      (chartData.notdefined || 0) +
      (chartData.ambiguous || 0) +
      (chartData.skipped || 0) ||
    chartData.rerun ||
    0;
  let title;

  if (total === 1) {
    title = total + ' ' + chartData.title.slice(0, -1);
  } else {
    title = total + ' ' + chartData.title;
  }

  let options = {
    width: '100%',
    height: 240,
    title: title,
    is3D: true,
    colors: ['#5cb85c', '#d9534f', '#999', '#5bc0de', '#428bca', '#f0ad4e', '#ff9933'],
    fontSize: '13',
    fontName: '"Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif',
    slices: {
      1: { offset: 0.4 },
      2: { offset: 0.4 },
      3: { offset: 0.4 },
      4: { offset: 0.4 },
      5: { offset: 0.4 },
      6: { offset: 0.4 },
      7: { offset: 0.4 },
    },
    titleTextStyle: {
      fontSize: '13',
      color: '#5e5e5e',
    },
  };

  let chart = new google.visualization.PieChart(document.getElementById('piechart_' + chartData.title.toLowerCase()));

  chart.draw(data, options);
}

      $('a.toggle').on('click', function (e) {
  e.preventDefault();

  if (!$(this).hasClass('collapse')) {
    if ($(this).text() === 'Screenshot -') {
      // $(this).text('Screenshot +');
      $(this).next('a.screenshot').find('img').hide();
    } else if ($(this).text() === 'Screenshot +') {
      // $(this).text('Screenshot -');
      $(this).next('a.screenshot').find('img').show();
    }
  }

  if ($(this).text().includes(' -')) {
    $(this).text($(this).text().replace(' -', ' +'));
  } else {
    $(this).text($(this).text().replace(' +', ' -'));
  }
});

  </script>
</body>

</html>
