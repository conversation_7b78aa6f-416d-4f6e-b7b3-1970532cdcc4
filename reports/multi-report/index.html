<!DOCTYPE html>
<html lang="en">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <!-- Meta, title, CSS, favicons, etc. -->
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta http-equiv="Cache-control" content="public">
        <title>GRO E2E Test Report</title>
        
                <!-- Bootstrap -->
                <link rel="stylesheet" href="assets/css/bootstrap.min.css" >
                <link rel="stylesheet" href="assets/css/dataTables.bootstrap.min.css" >
                <link rel="stylesheet" href="assets/css/responsive.bootstrap5.min.css" >

                <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
                <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->

                <!--[if lt IE 9]>
                <script src="assets/js/html5shiv.min.js"></script>
                <script src="assets/js/respond.min.js"></script>
                <![endif]-->
                <link href="assets/css/font-awesome.min.css" rel="stylesheet">
        

        <!-- Custom Theme Style -->
        <style type="text/css">
            body {
  color: #73879c;
  background: #f7f7f7;
  font-family: "Helvetica Neue", Roboto, Arial, "Droid Sans", sans-serif;
  font-size: 13px;
  font-weight: 400;
  line-height: 1.471;
}

.main_container {
  padding: 10px 20px 0;
}

i span {
  display: none;
}

/* Navigation */
nav.navbar {
  background: #ededed;
  border-bottom: 1px solid #d9dee4;
  margin-bottom: 0;
}

nav .navbar-brand {
  border-right: 1px solid #d9dee4;
  color: #5a738e;
}

nav .navbar-text {
  font-size: 18px;
  height: 50px;
  margin-bottom: 0;
  margin-top: 0;
  padding: 15px 0;
  float: right;
}

/* Table */
table {
  width: 100%;
}

table.chart tr th:first-of-type {
  width: 33.333%;
}

.table > thead > tr > th {
  background: #f5f7fa;
}

table.tile h3,
table.tile h4,
table.tile span {
  font-weight: bold;
  vertical-align: middle !important;
}

table.tile th,
table.tile td {
  text-align: center;
}

table.tile th {
  border-bottom: 1px solid #e6ecee;
}

table.tile td {
  padding: 5px 0;
}

table.tile td ul {
  text-align: left;
  padding-left: 0;
}

table.tile td ul li {
  list-style: none;
  width: 100%;
}

table.tile td ul li a {
  width: 100%;
}

table.tile td ul li a big {
  right: 0;
  float: right;
  margin-right: 13px;
}

table.tile_info {
  width: 100%;
}

table.tile_info td {
  text-align: left;
  padding: 1px;
  font-size: 15px;
}

table.tile_info td p {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 0;
  line-height: 28px;
}

table.tile_info td i {
  display: inline-block;
  margin-right: 8px;
  font-size: 17px;
  float: left;
  width: 18px;
  line-height: 28px;
  text-align: center;
}

table.tile_info td:first-child {
  width: 65%;
}

td span {
  line-height: 28px;
}

table.tile_info td.percentage {
  text-align: right;
}

/* chart in table */
table td.chart {
  display: inline-block;
  position: relative;
}

table td.chart #feature-chart,
table td.chart #scenario-chart,
table td.chart .total {
  height: 140px;
  margin: 15px 10px 10px 0;
  width: 140px;
}

table td.chart .total {
  display: inline-block;
  position: absolute;
  font-size: 2em;
  height: 50px;
  line-height: 50px;
  top: 45px;
  left: 45px;
  text-align: center;
  vertical-align: middle;
  width: 50px;
}

/* colors */
.ambiguous-color {
  color: #e74c3c !important;
}

.failed-color {
  color: #e74c3c !important;
}

.not-defined-color {
  color: #f39c12 !important;
}

.passed-color {
  color: #1abb9c !important;
}

.pending-color {
  color: #ffd119 !important;
}

.skipped-color {
  color: #3498db !important;
}

/* backgrounds */
.ambiguous-background {
  background: #b73122 !important;
}

.failed-background {
  background: #e74c3c !important;
}

.not-defined-background {
  background: #f39c12 !important;
}

.passed-background {
  background: #1abb9c !important;
}

.pending-background {
  background: #ffd119 !important;
}

.skipped-background {
  background: #3498db !important;
}

/* general */
.x_panel {
  position: relative;
  width: 100%;
  margin-bottom: 10px;
  padding: 10px 17px;
  display: inline-block;
  background: #fff;
  border: 1px solid #e6e9ed;
  -webkit-column-break-inside: avoid;
  opacity: 1;
}

.x_title {
  border-bottom: 2px solid #e6e9ed;
  padding: 1px 5px 6px;
  margin-bottom: 10px;
}

.x_title .filter {
  width: 40%;
  float: right;
}

.x_title h2 {
  margin: 5px 0 6px;
  float: left;
  font-size: 24px;
  font-weight: 400;
  display: block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.fixed_height_320 {
  height: 320px;
}

.x_title span {
  color: #bdbdbd;
}

.x_content {
  padding: 0 5px 6px;
  position: relative;
  width: 100%;
  float: left;
  clear: both;
  margin-top: 5px;
}

.x_content h4 {
  font-size: 16px;
  font-weight: 500;
}

.panel_toolbox {
  float: right;
  margin: 5px 0 0;
  min-width: 70px;
}

.panel_toolbox > li {
  float: right;
}

.panel_toolbox > li > a {
  cursor: pointer;
}

.panel_toolbox > li > a {
  padding: 5px;
  color: #c5c7cb;
  font-size: 14px;
}

.panel_toolbox > li > a:hover {
  background: #f5f7fa;
}

.page-title {
  width: 100%;
  padding: 10px 0 30px 0;
}

.page-title {
  display: block;
}

.page-title h1 {
  margin: 9px 0 9px 13px;
  font-size: 30px;
}

.page-title .title_right {
  width: 55%;
  float: left;
  display: block;
}

.page-title .title_right .pull-right {
  margin: 10px 0;
}

.page-title p {
  margin-left: 15px;
}

.dashboard-widget-content {
  padding-top: 9px;
}

.dashboard-widget-content .sidebar-widget {
  width: 50%;
  display: inline-block;
  vertical-align: top;
  background: #fff;
  border: 1px solid #abd9ea;
  border-radius: 5px;
  text-align: center;
  float: right;
  padding: 2px;
  margin-top: 10px;
}

ul.quick-list {
  padding-left: 0;
  display: inline-block;
}

ul.quick-list li,
table.quick-list tr {
  padding-left: 10px;
  list-style: none;
  margin: 0;
  padding-bottom: 6px;
  padding-top: 4px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

ul.quick-list li .meta-data-title,
table.quick-list td.meta-data-title {
  display: inline-block;
  min-width: 75px;
  font-weight: bold;
}

ul.quick-list li span,
table.quick-list td {
  line-height: 28px;
}

ul.quick-list li,
table.quick-list tr {
  border-bottom: 1px solid #efefef;
  padding: 0.5em 0;
}

ul.quick-list li:last-child,
table.quick-list tr:last-child {
  border-bottom: none;
}

ul.quick-list li i {
  padding-right: 10px;
  color: #757679;
}

.screenshot {
  max-height: 100%;
  max-width: 100%;
}

.videoCapture {
  width: 50%;
  height: 50%;
  max-height: 100%;
  max-width: 100%;
}

/* Features / Scenarios */
ul.panel_toolbox li .step {
  border-radius: 50%;
  color: #ffffff;
  display: block;
  font-size: 14px;
  height: 30px;
  margin-right: 5px;
  padding: 5px;
  text-align: center;
  width: 30px;
}

.scenario-step-container {
  margin-bottom: 10px;
}

.scenario-step-container .label {
  display: inline-block;
  text-align: center;
  width: 30px;
}

.scenario-step-container .text {
  display: inline;
}

.scenario-step-container .duration {
  position: relative;
  float: right;
}

.scenario-step-container .text .keyword.highlight {
  font-size: 1.2em;
  font-weight: 700;
}

.scenario-scroll-bar {
  overflow-x: scroll;
}

.scenario-step-collapse,
.scenario-scroll-bar .arguments {
  margin-left: 30px;
  width: auto;
}

/* media */
@media (max-width: 1200px) {
  .x_title h2 {
    width: 70%;
  }
}

@media (max-width: 640px) {
  .x_title h2 {
    width: 100%;
  }
}

/* override */
table.dataTable.dtr-inline.collapsed > tbody > tr > td:first-child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th:first-child:before {
  background: #1abb9c;
}

table.dataTable.dtr-inline.collapsed
  > tbody
  > tr.parent
  > td:first-child:before,
table.dataTable.dtr-inline.collapsed
  > tbody
  > tr.parent
  > th:first-child:before {
  background: #e74c3c;
}

.created-by {
  padding: 50px 0;
  text-align: center;
}

body.darkmode,
body.darkmode div.created-by,
body.darkmode .dropdown-menu {
  background: #212121 !important;
  color: #b4bfca;
  border-color: #2e2e2e;
}

body.darkmode .x_panel {
  background: #1c1c21 !important;
  color: #b4bfca;
  border: 1px solid #2e2e2e;
}

body.darkmode div.container-fluid {
  background: #2b2b2b !important;
  color: #b4bfca;
  border: 0 solid #2e2e2e;
}

body.darkmode nav.navbar {
  background: #2b2b2b;
  border-bottom: 1px solid #313a45;
  margin-bottom: 0;
}

body.darkmode .x_title {
  background: #1c1c21 !important;
  border-bottom: 2px solid #2e2e2e;
  padding: 1px 5px 6px;
  margin-bottom: 10px;
}

body.darkmode table.quick-list tr {
  border-bottom: 1px solid #292929;
  padding: 0.5em 0;
}

body.darkmode ul.quick-list li,
body.darkmode table.quick-list tr {
  border-bottom: 1px solid #2e2e2e;
  padding: 0.5em 0;
}

.darkModeIcon {
  font-size: x-large;
  float: left;
  padding-top: 12px;
  padding-left: 4px;
  cursor: pointer;
  color: #b4bfca;
  border-color: #2e2e2e;
}

.darkModeIcon:before {
  content: "";
}

body.darkmode a.navbar-default,
body.darkmode a.navbar-brand {
  color: #b1bfcd;
  background: transparent !important;
  border-right: 1px solid #313a45;
}

body.darkmode a {
  background: transparent !important;
  color: #9cc2e3;
  border-color: #2e2e2e;
}

body.darkmode a.collapse-link {
  color: #c7c7c7;
  border-color: #2e2e2e;
}

body.darkmode li:not([class]) > a[id] {
  color: white;
}
body.darkmode li:not([class]) > a[id]:hover {
  background: darkslateblue;
}

body.darkmode #features-table {
  color: #b4bfca;
  border-color: #2e2e2e;
}

body.darkmode #features-table th {
  background: #212121 !important;
  color: #a3c2db;
  border-color: #2e2e2e;
}

body.darkmode #features-table td {
  border-color: #2e2e2e;
}

body.darkmode #features-table tr:nth-of-type(odd) {
  background: #212121 !important;
  border-color: #2e2e2e;
}

body.darkmode table.dataTable > tbody > tr.child ul.dtr-details li {
  border-bottom: 1px solid #2e2e2e;
  padding: 0.5em 0;
}

body.darkmode .pagination,
body.darkmode .pagination > .active > a,
body.darkmode .pagination > .active > a:focus,
body.darkmode .pagination > .active > a:hover,
body.darkmode .pagination > .active > span,
body.darkmode .pagination > .active > span:focus,
body.darkmode .pagination > .active > span:hover {
  background: #3a7ab7;
  border-color: #3a7ab7;
}

body.darkmode .pagination > .disabled > a,
body.darkmode .pagination > .disabled > a:focus,
body.darkmode .pagination > .disabled > a:hover,
body.darkmode .pagination > .disabled > span,
body.darkmode .pagination > .disabled > span:focus,
body.darkmode .pagination > .disabled > span:hover {
  background: #3a7ab7;
  border-color: #3b3b3b;
  color: #bfbfbf;
}

body.darkmode
  table.dataTable.dtr-inline.collapsed
  > tbody
  > tr
  > td:first-child::before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th:first-child::before {
  border: 1px solid #2e2e2e;
}

body.darkmode #features-table > thead > tr {
  color: #b4bfca;
  border-color: #2e2e2e;
}

body.darkmode .form-control {
  background: #212121 !important;
  color: #c3c3c3;
  border-color: #2e2e2e;
}
body.darkmode li[id*="features"] > a {
  background: #212121 !important;
}

body.darkmode .btn-info {
  color: #b4bfca;
  background: #337ab7;
  border-color: #337ab7;
}

body.darkmode .panel_toolbox > li > a:hover {
  background: transparent;
}

body.darkmode .table-striped > tbody > tr:nth-of-type(even) {
  background: #1c1c21;
}

body.darkmode div pre {
  background: #212121 !important;
  color: #b4bfca;
}

body.darkmode span.tag {
  color: #b4bfca;
}

body.darkmode .keyword {
  color: #b4bfca;
}

body.darkmode div.tags ~ h1 {
  color: #b4bfca;
}

body.darkmode div.tags ~ h2 {
  color: #b4bfca;
}

body.darkmode div.tags ~ h1 small {
  color: #bfbfbf;
}

body.darkmode div.tags ~ h2 small {
  color: #bfbfbf;
}

svg#moon,
svg#sun {
  width: 40px;
  height: 30px;
  fill: #ec6d04;
}

input#darkCheck:checked ~ label > svg#sun {
  display: none;
  border-color: #212121;
}

label > svg#moon {
  display: none;
}

input#darkCheck:checked ~ label > svg#moon {
  display: inline-block;
  fill: #b4bfca;
  border-color: #212121;
}

            
        </style>
    </head>
    <body>
        <nav class="navbar">
            <div class="container-fluid">
                <p class="navbar-text" style="float: left">Dashboard</p>
                <input id="darkCheck" type="checkbox" hidden onchange="applyDarkMode(); saveState()"></input>
                <label title="Darkmode" id=darkmodeicon for="darkCheck" class="darkModeIcon">
                    <svg id="moon" shape-rendering="geometricPrecision" text-rendering="geometricPrecision" image-rendering="optimizeQuality" fill-rule="evenodd" clip-rule="evenodd" viewBox="0 0 512 262.86"><path fill-rule="nonzero" d="M316.78 16.55h-205.9c-30.5 0-58.22 12.48-78.31 32.57C12.47 69.21 0 96.93 0 127.44c0 30.5 12.47 58.22 32.57 78.31 20.09 20.1 47.81 32.57 78.31 32.57h193.25c21.54 15.43 47.9 24.54 76.26 24.54h.18c36.14 0 69.02-14.79 92.83-38.6 23.8-23.81 38.6-56.67 38.6-92.83 0-36.15-14.78-69.03-38.63-92.8C449.53 14.8 416.67 0 380.57 0h-.18c-23.02 0-44.72 6.02-63.61 16.55zm70.62 97.17.43.09c.82-3.45 2.83-6.19 6.04-8.16 3.2-1.98 6.53-2.57 10.01-1.75l.1-.43c-3.47-.82-6.2-2.83-8.17-6.03-1.98-3.22-2.57-6.55-1.75-10.01l-.43-.1c-.82 3.47-2.83 6.2-6.03 8.18-3.21 1.98-6.55 2.56-10.02 1.74l-.1.43c3.47.82 6.2 2.84 8.18 6.04 1.99 3.19 2.56 6.52 1.74 10zm36.87 16.77.53.12c1.02-4.35 3.55-7.78 7.58-10.26 4.02-2.49 8.2-3.22 12.56-2.19l.13-.53c-4.35-1.03-7.78-3.55-10.26-7.59-2.49-4.03-3.22-8.22-2.2-12.56l-.53-.12c-1.02 4.35-3.55 7.77-7.58 10.26-4.02 2.49-8.21 3.22-12.56 2.19l-.13.53c4.36 1.03 7.78 3.55 10.26 7.58 2.49 4.02 3.22 8.22 2.2 12.57zm-38.79-61.01c-15.69 7.67-26.98 23.26-28.29 41.93-1.96 27.88 19.05 52.06 46.92 54.02 13.23.93 25.64-3.32 35.22-11.02 4.75-3.82 9.66-.45 7.59 4.36-11.33 26.42-38.45 44.04-68.74 41.91-38.29-2.69-67.14-35.91-64.45-74.19C316.3 89.8 347.05 61.67 383.44 62c6.71.06 8.13 4.5 2.04 7.48zm-5.09-53.95h.18c63.75 0 115.91 52.15 115.91 115.9 0 63.75-52.23 115.91-115.91 115.91h-.18c-63.68 0-115.91-52.16-115.91-115.91s52.16-115.9 115.91-115.9z"/></svg>
                    <svg id="sun" shape-rendering="geometricPrecision" text-rendering="geometricPrecision" image-rendering="optimizeQuality" fill-rule="evenodd" clip-rule="evenodd" viewBox="0 0 512 256.04"><path d="M128.02 0h.18c22.03 0 42.83 5.66 61 15.6h210.38c30.89 0 59 12.65 79.38 33.04C499.35 68.99 512 97.1 512 128.02c0 30.92-12.66 59.03-33.02 79.4l-.42.38c-20.34 20.15-48.29 32.64-78.98 32.64H189.24c-18.17 9.93-38.98 15.6-61.04 15.6h-.18c-35.2 0-67.22-14.41-90.42-37.6C14.41 195.25 0 163.24 0 128.02s14.4-67.24 37.59-90.43l.91-.83C61.65 14.05 93.29 0 128.02 0zm-5.95 54.42c0-1.95.8-3.73 2.08-5 2.74-2.77 7.27-2.76 10.02-.01l.14.16a7.042 7.042 0 0 1 1.94 4.85v12.95c0 1.95-.8 3.73-2.08 5.01-2.75 2.75-7.27 2.75-10.02 0a7.084 7.084 0 0 1-2.08-5.01V54.42zm6.05 31.17c11.72 0 22.32 4.75 30 12.43 7.67 7.68 12.43 18.29 12.43 30 0 11.72-4.75 22.32-12.43 30s-18.28 12.43-30 12.43c-11.72 0-22.32-4.75-30.01-12.43-7.67-7.68-12.43-18.28-12.43-30 0-11.72 4.76-22.32 12.43-30 7.69-7.67 18.3-12.43 30.01-12.43zm-56.33-5.34a7.114 7.114 0 0 1-2.07-5.01c0-3.9 3.18-7.09 7.09-7.09 1.81 0 3.62.69 5 2.07l9.16 9.16a7.065 7.065 0 0 1 2.08 5.01c0 1.8-.7 3.62-2.08 5.01a7.057 7.057 0 0 1-5.01 2.08c-1.8 0-3.61-.7-5-2.07l-9.17-9.16zm-17.28 53.81c-1.95 0-3.73-.8-5-2.08-2.77-2.74-2.76-7.27-.01-10.01l.15-.14a7.04 7.04 0 0 1 4.86-1.94h12.94a7.082 7.082 0 0 1 7.09 7.09c0 1.95-.8 3.73-2.07 5.01a7.099 7.099 0 0 1-5.02 2.07H54.51zm25.82 50.28a7.049 7.049 0 0 1-5 2.07c-3.91 0-7.09-3.16-7.09-7.08 0-1.81.68-3.62 2.07-5.01l9.31-9.29a7.02 7.02 0 0 1 4.86-1.94 7.09 7.09 0 0 1 7.09 7.09c0 1.79-.69 3.6-2.08 4.99l-9.16 9.17zm53.82 17.29c0 1.94-.8 3.73-2.08 5-2.74 2.76-7.27 2.75-10.02 0l-.13-.15a7.033 7.033 0 0 1-1.94-4.85v-12.95c0-1.96.8-3.73 2.07-5.01 2.76-2.75 7.27-2.75 10.03 0a7.1 7.1 0 0 1 2.07 5.01v12.95zm50.28-25.83a7.055 7.055 0 0 1 2.07 5.01c0 3.89-3.18 7.09-7.08 7.09-1.81 0-3.63-.69-5.01-2.07l-9.16-9.16a7.095 7.095 0 0 1-2.07-5.02c0-3.9 3.18-7.09 7.08-7.09 1.8 0 3.61.7 5 2.08l9.17 9.16zm17.29-53.82c1.93 0 3.73.81 5 2.08 2.76 2.75 2.75 7.27 0 10.02l-.15.14a7.098 7.098 0 0 1-4.85 1.94h-12.95c-1.96 0-3.74-.8-5.01-2.08-2.76-2.75-2.76-7.27 0-10.02a7.049 7.049 0 0 1 5.01-2.08h12.95zM175.89 71.7a7.074 7.074 0 0 1 5-2.07c3.9 0 7.1 3.19 7.1 7.09 0 1.81-.69 3.62-2.07 5l-9.32 9.31a7.12 7.12 0 0 1-4.86 1.93c-3.91 0-7.09-3.18-7.09-7.09 0-1.8.7-3.61 2.08-5l9.16-9.17zm34.17-41.87c2.96 2.47 5.81 5.07 8.53 7.8 23.22 23.15 37.63 55.17 37.63 90.39s-14.42 67.23-37.6 90.42a130.2 130.2 0 0 1-8.5 7.77h189.46c26.83 0 51.24-10.91 69.02-28.5l.32-.35c17.79-17.79 28.85-42.35 28.85-69.34 0-26.99-11.06-51.55-28.85-69.35-17.77-17.8-42.33-28.84-69.34-28.84H210.06zm-82.04-14.71h.18c62.09 0 112.89 50.81 112.89 112.9 0 62.1-50.86 112.9-112.89 112.9h-.18c-62.03 0-112.9-50.8-112.9-112.9 0-62.09 50.81-112.9 112.9-112.9z"/></svg>
                </label>
                <p class="navbar-text">GRO E2E Test Report</p>
            </div>
        </nav>

        <div class="main_container">

            <div class="row">
                <div class="col-md-6 col-lg-4 col-xs-12">
                    <div class="x_panel fixed_height_320">
                        <div class="x_title">
    <h2>Features</h2>
    <ul class="nav navbar-right panel_toolbox">
        <li>
            <a class="collapse-link">
                <i class="fa fa-chevron-up"></i>
            </a>
        </li>
    </ul>
    <div class="clearfix"></div>
</div>
<div class="x_content">
    <table class="chart">
        <tr>
            <th>
                <p>Chart</p>
            </th>
            <th>
                <div class="col-lg-7 col-md-7 col-sm-7 col-xs-7">
                    <p class="">Status</p>
                </div>
                <div class="col-lg-5 col-md-5 col-sm-5 col-xs-5">
                    <p class="" style="float:right;">Progress</p>
                </div>
            </th>
        </tr>
        <tr>
            <td class="chart">
                <canvas id="feature-chart"></canvas>
                <div class="total">1</div>
            </td>
            <td>
                <table class="tile_info">
                    <tr>
                        <td>
                            <p data-bs-toggle="tooltip" data-placement="left" title="Features passed">
                                <i class="fa fa-check-circle passed-color"></i>
                                Passed
                            </p>
                        </td>
                        <td class="percentage">0.00 %</td>
                    </tr>
                    <tr>
                        <td>
                            <p data-bs-toggle="tooltip" data-placement="left" title="Features failed">
                                <i class="fa fa-exclamation-circle failed-color"></i>
                                Failed
                            </p>
                        </td>
                        <td class="percentage">0.00 %</td>
                    </tr>
                    
                    
                    
                    
                    <tr>
                        <td>
                            <p data-bs-toggle="tooltip" data-placement="left"
                               title="Features skipped">
                                <i class="fa fa-arrow-circle-right skipped-color"></i>
                                Skipped
                            </p>
                        </td>
                        <td class="percentage">100.00 %</td>
                    </tr>
                    

                </table>
            </td>
        </tr>
    </table>
</div>

                    </div>
                </div>

                <div class="col-md-6 col-lg-4 col-xs-12">
                    <div class="x_panel fixed_height_320">
                        <div class="x_title">
    <h2>Scenarios</h2>
    <ul class="nav navbar-right panel_toolbox">
        <li>
            <a class="collapse-link">
                <i class="fa fa-chevron-up"></i>
            </a>
        </li>
    </ul>
    <div class="clearfix"></div>
</div>
<div class="x_content">
    <table class="chart">
        <tr>
            <th>
                <p>Chart</p>
            </th>
            <th>
                <div class="col-lg-7 col-md-7 col-sm-7 col-xs-7">
                    <p class="">Status</p>
                </div>
                <div class="col-lg-5 col-md-5 col-sm-5 col-xs-5">
                    <p class="" style="float:right;">Progress</p>
                </div>
            </th>
        </tr>
        <tr>
            <td class="chart">
                <canvas id="scenario-chart"></canvas>
                <div class="total">1</div>
            </td>
            <td>
                <table class="tile_info">
                    <tr>
                        <td>
                            <p data-bs-toggle="tooltip" data-placement="left" title="Scenario passed">
                                <i class="fa fa-check-circle passed-color"></i>
                                Passed
                            </p>
                        </td>
                        <td class="percentage">0.00 %</td>
                    </tr>
                    <tr>
                        <td>
                            <p data-bs-toggle="tooltip" data-placement="left" title="Scenario failed">
                                <i class="fa fa-exclamation-circle failed-color"></i>
                                Failed
                            </p>
                        </td>
                        <td class="percentage">0.00 %</td>
                    
                    
                    </tr>
                    
                    
                    <tr>
                        <td>
                            <p data-bs-toggle="tooltip" data-placement="left" title="Scenario is skipped">
                                <i class="fa fa-arrow-circle-right skipped-color"></i>
                                Skipped
                            </p>
                        </td>
                        <td class="percentage">100.00 %</td>
                    </tr>
                    
                </table>
            </td>
        </tr>
        
    </table>
</div>

                    </div>
                </div>

                
                <div class="col-lg-4 col-xs-12">
                    <div class="x_panel fixed_height_320">
                        
<div class="x_title">
    <h2>Run info</h2>
    <ul class="nav navbar-right panel_toolbox">
        <li>
            <a class="collapse-link">
                <i class="fa fa-chevron-up"></i>
            </a>
        </li>
    </ul>
    <div class="clearfix"></div>
</div>
<div class="x_content">
    <div class="dashboard-widget-content">
        <table class="quick-list">
            
                
                <tr>
                    <td class="meta-data-title">Project</td>
                    <td class="meta-data-data">GRO E2E Testing</td>
                </tr>
                
                <tr>
                    <td class="meta-data-title">Environment</td>
                    <td class="meta-data-data">dev</td>
                </tr>
                
                <tr>
                    <td class="meta-data-title">Execution Date</td>
                    <td class="meta-data-data">2025-05-29</td>
                </tr>
                
            
        </table>
    </div>
</div>


                    </div>
                </div>
                
            </div>

            <div class="row">
                <div class="col-md-12 col-sm-12 col-xs-12">
    <div class="x_panel">
        <div class="x_title">
            <h2>Features overview</h2>
            <ul class="nav navbar-right panel_toolbox">
                <li>
                    <a class="collapse-link">
                        <i class="fa fa-chevron-up"></i>
                    </a>
                </li>
            </ul>
            <div class="clearfix"></div>
        </div>

        <div class="x_content">
            <div class="table-responsive">
                <table id="features-table" class="table table-striped table-bordered dt-responsive nowrap" cellspacing="0"
                       width="100%">
                    <thead>
                    <tr>
                        <th>Feature name</th>
                        <th><i class="fa fa-tags fa-lg" title="Tags"></i></th>
                        <th>Status</th>
                        
                            <th class="text-center">
                                <i class="fa fa-desktop fa-lg"></i>
                                <i class="fa fa-mobile fa-lg"></i>
                            </th>
                            <th>Device</th>
                            <th>OS</th>
                            
                            
                            <th>Browser</th>
                            
                        
                        
                        <th>Date</th>
                        
                        
                        <th>Duration</th>
                        
                        <th>Total</th>
                        <th>Passed</th>
                        <th>Failed</th>
                        
                        <th>Skip</th>
                        
                        
                        
                        
                    </tr>
                    </thead>

                    <tbody>
                    
                    <tr>
                        <td>
                            <a href="features/e83170f3-a8a8-4cec-8533-57b2582e7133-login-to-gro.html">Login to GRO</a>
                        </td>
                        <td class="text-center">
                            
                            
                            
                            
                            
                            
                            
                            
                            
                            
                            
                            
                            
                            
                            
                            <i class="fa fa-tags fa-lg" data-bs-toggle="tooltip" data-placement="top"
                               title="@LoginSanity @SanityTest @Smoke">
                                <span>@LoginSanity @SanityTest @Smoke</span>
                            </i>
                            
                            
                        </td>
                        <td class="text-center">
                            
                            
                            
                            
                            
                            
                            <i class="fa fa-arrow-circle-right skipped-color fa-lg" data-bs-toggle="tooltip" data-placement="top" title="Skipped">
                                <span>arrow-circle-right skipped-color</span>
                            </i>
                        </td>
                        
                            <td class="text-center">
                                
                                
                                
                                
                                
                                
                                <i class="fa fa-desktop fa-lg" data-bs-toggle="tooltip" data-placement="top" title="Desktop">
                                    <span>desktop</span>
                                </i>
                            </td>
                            <td>
                                
                                
                                
                                
                                Local test machine
                            </td>
                            <td>
                                
                                
                                
                                
                                <i class="fa fa-windows fa-lg"><span>windows</span></i>
                                10
                            </td>
                            
                            
                            <td>
                                
                                
                                
                                
                                
                                
                                <i class="fa fa-chrome fa-lg">
                                    <span>chrome</span></i>
                                116
                                

                                
                            </td>
                            
                        
                        
                        <td>2025/05/29 14:38:54</td>
                        
                        
                        <td>00:00:00.000</td>
                        
                        <td class="text-right ">1</td>
                        <td class="text-right none">0</td>
                        <td class="text-right none">0</td>
                        
                        <td class="text-right ">1</td>
                        
                        
                        
                        
                    </tr>
                    
                    </tbody>

                </table>
            </div>
        </div>
    </div>
</div>

            </div>
        </div>
         
            <div class="created-by">
                <p>Created by wswebcreation. Find me on:</p>
                <a href="http://www.wswebcreation.nl/" target="_blank"><i class="fa fa-rss-square fa-2x"></i></a>
                <a href="https://github.com/wswebcreation/" target="_blank"><i class="fa fa-github-square fa-2x"></i></a>
                <a href="http://nl.linkedin.com/in/wimselles" target="_blank"><i class="fa fa-linkedin-square fa-2x"></i></a>
                <a href="http://stackoverflow.com/users/5911978/wswebcreation" target="_blank"><i class="fa fa-stack-overflow fa-2x"></i></a>
            </div>
         

        
            <script src="assets/js/jquery.min.js"></script>
            <script src="assets/js/bootstrap.min.js"></script>
            <script src="assets/js/Chart.min.js"></script>

            <script src="assets/js/datatables.jquery.min.js"></script>
            <script src="assets/js/datatables.min.js"></script>
            <script src="assets/js/datatables.bootstrap5.min.js"></script>
            <script src="assets/js/dataTables.responsive.min.js"></script>
            <script src="assets/js/responsive.bootstrap5.js"></script>
        

        <!-- Darkmode -->
        <script>
            var darkMode = 'darkmode'

function applyDarkMode() {
    document.getElementById('features-table').classList.toggle('table-striped');
    applyFontStyle();
}

function saveState() {
    if(isDarkModeOn()) {
        window.localStorage['darkmode'] = 'on';
    } else {
        window.localStorage['darkmode'] = 'off';
    }
}

function applyFontStyle() {
    document.body.classList.toggle(darkMode);
}


function isDarkModeOn() {
    var toggle = document.getElementById('darkCheck');
    return toggle.checked;
}

window.onload = function() {
    if(window.localStorage['darkmode'] === 'on') {
        applyDarkMode();
        document.getElementById('darkCheck').checked = true;
    }
}

        </script>

        <!-- Custom -->
        <script>
            $(document).ready(function () {
                $('#features-table').dataTable({
                    "order": [[0, "asc"]],
                    "lengthMenu": [[50, 100, 150, -1], [50, 100, 150, "All"]],
                    "stateSave": true
                });

                var featureOptions = {
                    legend: false,
                    responsive: false
                };

                var getColor = function(selector, defaultColor) {
                    if (document.querySelector(selector)) {
                        return getComputedStyle(document.querySelector(selector)).color
                    }
                    return defaultColor
                }

                new Chart(document.getElementById("feature-chart"), {
                    type: 'doughnut',
                    tooltipFillColor: "rgba(51, 51, 51, 0.55)",
                    data: {
                        labels: [
                            "Passed",
                            "Failed",
                            "Pending",
                            "Skipped",
                            "Ambiguous",
                            "Not Defined"
                        ],
                        datasets: [{
                            data: [
                                0,
                                0,
                                0,
                                1,
                                0,
                                0
                            ],
                            backgroundColor: [
                                getColor(".passed-color", "#26B99A"),
                                getColor(".failed-color", "#E74C3C"),
                                getColor(".pending-color", "#FFD119"),
                                getColor(".skipped-color", "#3498DB"),
                                getColor(".ambiguous-color", "#b73122"),
                                getColor(".not-defined-color", "#F39C12")
                            ]
                        }]
                    },
                    options: featureOptions
                });

                var scenarioOptions = {
                    legend: false,
                    responsive: false
                };

                new Chart(document.getElementById("scenario-chart"), {
                    type: 'doughnut',
                    tooltipFillColor: "rgba(51, 51, 51, 0.55)",
                    data: {
                        labels: [
                            "Passed",
                            "Failed",
                            "Pending",
                            "Skipped",
                            "Ambiguous",
                            "Not Defined"
                        ],
                        datasets: [{
                            data: [
                                0,
                                0,
                                0,
                                1,
                                0,
                                0
                            ],
                            backgroundColor: [
                                getColor(".passed-color", "#26B99A"),
                                getColor(".failed-color", "#E74C3C"),
                                getColor(".pending-color", "#FFD119"),
                                getColor(".skipped-color", "#3498DB"),
                                getColor(".ambiguous-color", "#b73122"),
                                getColor(".not-defined-color", "#F39C12")
                            ]
                        }]
                    },
                    options: scenarioOptions
                });

                $(".x_title").on("click", function () {
  var $BOX_PANEL = $(this).closest(".x_panel"),
    $ICON = $(this).find(".collapse-link i"),
    $BOX_CONTENT = $BOX_PANEL.find(".x_content");

  // fix for some div with hardcoded fix class
  if ($BOX_PANEL.attr("style")) {
    $BOX_CONTENT.slideToggle(200, function () {
      $BOX_PANEL.removeAttr("style");
    });
  } else {
    $BOX_CONTENT.slideToggle(200);
    $BOX_PANEL.css("height", "auto");
  }

  $ICON.toggleClass("fa-chevron-up fa-chevron-down");
});

$("body").tooltip({
  selector: '[data-bs-toggle="tooltip"]',
});

hideResult = (resultId) => {
  $("span[class*=step]").closest("div.x_panel[style]").hide();
  $("span[class*=" + resultId + "]")
    .closest("div.x_panel[style]")
    .show();
};

showAll = () => {
  $("span[class*=step]").closest("div.x_panel[style]").show();
};

$(document).ready(() => {
  const status = [
    "passed",
    "failed",
    "pending",
    "skipped",
    "ambiguous",
    "not-defined",
  ];
  status.forEach((value) => {
    var menuItem = $("span[class*=" + value + "-background]");
    if (menuItem.length === 0) {
      $("#" + value)
        .parent()
        .addClass("disabled");
    }
  });
});

            });
        </script>
    </body>
</html>
