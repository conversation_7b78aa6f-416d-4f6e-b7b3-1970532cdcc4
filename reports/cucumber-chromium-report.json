[{"description": "", "elements": [{"description": "", "id": "login-to-gro;user-logs-in-to-gro-using-test-data-by-id", "keyword": "Scenario Outline", "line": 12, "name": "User logs in to GRO using test data by ID", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 980656634}}, {"arguments": [], "keyword": "Given ", "line": 5, "name": "access to GRO login page \"/groNg/#/login\"", "match": {"location": "features/steps/login.steps.ts:15"}, "result": {"status": "passed", "duration": 1661404694}}, {"arguments": [], "keyword": "When ", "line": 6, "name": "user enters valid login data for ID \"1001\"", "match": {"location": "features/steps/login.steps.ts:19"}, "result": {"status": "failed", "duration": 5010679274, "error_message": "Error: function timed out, ensure the promise resolves within 5000 milliseconds\n    at Timeout.<anonymous> (/Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/node_modules/@cucumber/cucumber/src/time.ts:52:14)\n    at listOnTimeout (node:internal/timers:608:17)\n    at processTimers (node:internal/timers:543:7)"}}, {"arguments": [], "keyword": "Then ", "line": 7, "name": "user should be logged in successfully", "match": {"location": "features/steps/login.steps.ts:27"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 8, "name": "user should see the home page", "match": {"location": "features/steps/login.steps.ts:31"}, "result": {"status": "skipped", "duration": 0}}, {"keyword": "After", "hidden": true, "result": {"status": "passed", "duration": 151896373}}], "tags": [{"name": "@Smoke", "line": 3}, {"name": "@Regression", "line": 3}, {"name": "@dev", "line": 3}, {"name": "@stg", "line": 3}, {"name": "@prod", "line": 3}], "type": "scenario"}, {"description": "", "id": "login-to-gro;user-logs-in-to-gro-using-test-data-by-id", "keyword": "Scenario Outline", "line": 13, "name": "User logs in to GRO using test data by ID", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 919297235}}, {"arguments": [], "keyword": "Given ", "line": 5, "name": "access to GRO login page \"/groNg/#/login\"", "match": {"location": "features/steps/login.steps.ts:15"}, "result": {"status": "passed", "duration": 1682078756}}, {"arguments": [], "keyword": "When ", "line": 6, "name": "user enters valid login data for ID \"1002\"", "match": {"location": "features/steps/login.steps.ts:19"}, "result": {"status": "failed", "duration": 1182168, "error_message": "Error: No login user found for ID \"1002\" in prod-credentials.json\n    at getLoginDataById (/Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/features/support/loginDataLoader.ts:21:11)\n    at CustomWorld.<anonymous> (/Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/features/steps/login.steps.ts:20:33)\n    at /Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/features/steps/login.steps.ts:41:71\n    at __awaiter (/Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/features/steps/login.steps.ts:37:12)\n    at CustomWorld.<anonymous> (/Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/features/steps/login.steps.ts:61:12)"}}, {"arguments": [], "keyword": "Then ", "line": 7, "name": "user should be logged in successfully", "match": {"location": "features/steps/login.steps.ts:27"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 8, "name": "user should see the home page", "match": {"location": "features/steps/login.steps.ts:31"}, "result": {"status": "skipped", "duration": 0}}, {"keyword": "After", "hidden": true, "result": {"status": "passed", "duration": 81275080}}], "tags": [{"name": "@Smoke", "line": 3}, {"name": "@Regression", "line": 3}, {"name": "@dev", "line": 3}, {"name": "@stg", "line": 3}, {"name": "@prod", "line": 3}], "type": "scenario"}, {"description": "", "id": "login-to-gro;user-logs-in-to-gro-using-test-data-by-id", "keyword": "Scenario Outline", "line": 14, "name": "User logs in to GRO using test data by ID", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 928938137}}, {"arguments": [], "keyword": "Given ", "line": 5, "name": "access to GRO login page \"/groNg/#/login\"", "match": {"location": "features/steps/login.steps.ts:15"}, "result": {"status": "passed", "duration": 1606063882}}, {"arguments": [], "keyword": "When ", "line": 6, "name": "user enters valid login data for ID \"1003\"", "match": {"location": "features/steps/login.steps.ts:19"}, "result": {"status": "failed", "duration": 845266, "error_message": "Error: No login user found for ID \"1003\" in prod-credentials.json\n    at getLoginDataById (/Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/features/support/loginDataLoader.ts:21:11)\n    at CustomWorld.<anonymous> (/Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/features/steps/login.steps.ts:20:33)\n    at /Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/features/steps/login.steps.ts:41:71\n    at __awaiter (/Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/features/steps/login.steps.ts:37:12)\n    at CustomWorld.<anonymous> (/Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/features/steps/login.steps.ts:61:12)"}}, {"arguments": [], "keyword": "Then ", "line": 7, "name": "user should be logged in successfully", "match": {"location": "features/steps/login.steps.ts:27"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 8, "name": "user should see the home page", "match": {"location": "features/steps/login.steps.ts:31"}, "result": {"status": "skipped", "duration": 0}}, {"keyword": "After", "hidden": true, "result": {"status": "passed", "duration": 89305635}}], "tags": [{"name": "@Smoke", "line": 3}, {"name": "@Regression", "line": 3}, {"name": "@dev", "line": 3}, {"name": "@stg", "line": 3}, {"name": "@prod", "line": 3}], "type": "scenario"}], "id": "login-to-gro", "line": 1, "keyword": "Feature", "name": "Login to GRO", "tags": [], "uri": "features/login.feature"}]