[{"description": "", "elements": [{"description": "", "id": "login-to-gro;user-logs-in-to-gro-using-test-data-by-id", "keyword": "Scenario Outline", "line": 12, "name": "User logs in to GRO using test data by ID", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 1022760305}}, {"arguments": [], "keyword": "Given ", "line": 5, "name": "access to GRO login page \"/groNg/#/login\"", "match": {"location": "features/steps/login.steps.ts:15"}, "result": {"status": "passed", "duration": 1748899779}}, {"arguments": [], "keyword": "When ", "line": 6, "name": "user enters valid login data for ID \"1001\"", "match": {"location": "features/steps/login.steps.ts:19"}, "result": {"status": "passed", "duration": 4713200344}}, {"arguments": [], "keyword": "Then ", "line": 7, "name": "user should be logged in successfully", "match": {"location": "features/steps/login.steps.ts:34"}, "result": {"status": "passed", "duration": 53621984}}, {"arguments": [], "keyword": "And ", "line": 8, "name": "user should see the home page", "match": {"location": "features/steps/login.steps.ts:38"}, "result": {"status": "passed", "duration": 29891793}}, {"keyword": "After", "hidden": true, "result": {"status": "passed", "duration": 409304133}}], "tags": [{"name": "@Smoke", "line": 3}, {"name": "@Regression", "line": 3}, {"name": "@dev", "line": 3}, {"name": "@stg", "line": 3}, {"name": "@prod", "line": 3}], "type": "scenario"}], "id": "login-to-gro", "line": 1, "keyword": "Feature", "name": "Login to GRO", "tags": [], "uri": "features/login.feature"}]