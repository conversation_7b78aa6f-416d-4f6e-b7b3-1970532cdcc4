[{"description": "", "elements": [{"description": "", "id": "login-to-gro;user-logs-in-to-gro-using-test-data-by-id", "keyword": "Scenario Outline", "line": 12, "name": "User logs in to GRO using test data by ID", "steps": [{"arguments": [], "keyword": "Given ", "line": 5, "name": "access to GRO login page \"/groNg/#/login\"", "match": {"location": "features/steps/login.steps.ts:15"}, "result": {"status": "failed", "duration": 9253794, "error_message": "TypeError: Cannot read properties of undefined (reading 'goto')\n    at World.<anonymous> (/Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/features/steps/login.steps.ts:16:19)\n    at /Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/features/steps/login.steps.ts:41:71\n    at __awaiter (/Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/features/steps/login.steps.ts:37:12)\n    at World.<anonymous> (/Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/features/steps/login.steps.ts:56:12)"}}, {"arguments": [], "keyword": "When ", "line": 6, "name": "user enters valid login data for ID \"1001\"", "match": {"location": "features/steps/login.steps.ts:19"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 7, "name": "user should be logged in successfully", "match": {"location": "features/steps/login.steps.ts:27"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 8, "name": "user should see the home page", "match": {"location": "features/steps/login.steps.ts:31"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@Smoke", "line": 3}, {"name": "@Regression", "line": 3}, {"name": "@dev", "line": 3}, {"name": "@stg", "line": 3}, {"name": "@prod", "line": 3}], "type": "scenario"}, {"description": "", "id": "login-to-gro;user-logs-in-to-gro-using-test-data-by-id", "keyword": "Scenario Outline", "line": 13, "name": "User logs in to GRO using test data by ID", "steps": [{"arguments": [], "keyword": "Given ", "line": 5, "name": "access to GRO login page \"/groNg/#/login\"", "match": {"location": "features/steps/login.steps.ts:15"}, "result": {"status": "failed", "duration": 397234, "error_message": "TypeError: Cannot read properties of undefined (reading 'goto')\n    at World.<anonymous> (/Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/features/steps/login.steps.ts:16:19)\n    at /Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/features/steps/login.steps.ts:41:71\n    at __awaiter (/Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/features/steps/login.steps.ts:37:12)\n    at World.<anonymous> (/Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/features/steps/login.steps.ts:56:12)"}}, {"arguments": [], "keyword": "When ", "line": 6, "name": "user enters valid login data for ID \"1002\"", "match": {"location": "features/steps/login.steps.ts:19"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 7, "name": "user should be logged in successfully", "match": {"location": "features/steps/login.steps.ts:27"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 8, "name": "user should see the home page", "match": {"location": "features/steps/login.steps.ts:31"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@Smoke", "line": 3}, {"name": "@Regression", "line": 3}, {"name": "@dev", "line": 3}, {"name": "@stg", "line": 3}, {"name": "@prod", "line": 3}], "type": "scenario"}, {"description": "", "id": "login-to-gro;user-logs-in-to-gro-using-test-data-by-id", "keyword": "Scenario Outline", "line": 14, "name": "User logs in to GRO using test data by ID", "steps": [{"arguments": [], "keyword": "Given ", "line": 5, "name": "access to GRO login page \"/groNg/#/login\"", "match": {"location": "features/steps/login.steps.ts:15"}, "result": {"status": "failed", "duration": 290187, "error_message": "TypeError: Cannot read properties of undefined (reading 'goto')\n    at World.<anonymous> (/Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/features/steps/login.steps.ts:16:19)\n    at /Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/features/steps/login.steps.ts:41:71\n    at __awaiter (/Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/features/steps/login.steps.ts:37:12)\n    at World.<anonymous> (/Users/<USER>/Compsych QA/GRO/GRO-E2E-01-1/features/steps/login.steps.ts:56:12)"}}, {"arguments": [], "keyword": "When ", "line": 6, "name": "user enters valid login data for ID \"1003\"", "match": {"location": "features/steps/login.steps.ts:19"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "Then ", "line": 7, "name": "user should be logged in successfully", "match": {"location": "features/steps/login.steps.ts:27"}, "result": {"status": "skipped", "duration": 0}}, {"arguments": [], "keyword": "And ", "line": 8, "name": "user should see the home page", "match": {"location": "features/steps/login.steps.ts:31"}, "result": {"status": "skipped", "duration": 0}}], "tags": [{"name": "@Smoke", "line": 3}, {"name": "@Regression", "line": 3}, {"name": "@dev", "line": 3}, {"name": "@stg", "line": 3}, {"name": "@prod", "line": 3}], "type": "scenario"}], "id": "login-to-gro", "line": 1, "keyword": "Feature", "name": "Login to GRO", "tags": [], "uri": "features/login.feature"}]