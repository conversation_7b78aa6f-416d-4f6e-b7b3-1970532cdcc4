import { test, expect } from '@playwright/test';

// GROPR1 Login Test for PROD environment with Smoke and Regression tags
// This mirrors the Cucumber test: user 1001 login with @Smoke @Regression @prod tags

test.describe('GROPR1 Login - Smoke & Regression Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // Set longer timeout for all tests
    test.setTimeout(60000);
  });

  test('GROPR1 (User 1001) can login successfully in PROD - Smoke Test', {
    tag: ['@smoke', '@regression', '@prod', '@user1001']
  }, async ({ page }) => {
    // Navigate to GRO login page
    await page.goto('/groNg/#/login');

    // Wait for page to load
    await page.waitForLoadState('networkidle');

    // Use GROPR1 credentials (User 1001)
    const username = 'GROPR1';
    const password = 'Testtest1';

    // Fill in login form
    await page.locator('input[formcontrolname="userName"]').fill(username, { timeout: 10000 });
    await page.locator('input[formcontrolname="password"]').fill(password, { timeout: 10000 });

    // Click login button
    await page.locator('button[type="submit"]').click({ timeout: 10000 });

    // Wait for navigation to complete
    await page.waitForURL('**/home', { timeout: 30000 });

    // Verify successful login - check for home page elements
    await expect(page.locator('h1')).toContainText('to live services & care options', { timeout: 15000 });

    // Verify we're on the home page
    await expect(page).toHaveURL(/groNg\/#\/home/<USER>

    // Take a screenshot for verification
    await page.screenshot({ path: 'test-results/gropr1-login-success.png', fullPage: true });
  });

  test('GROPR1 (User 1001) login page loads correctly - Regression Test', {
    tag: ['@smoke', '@regression', '@prod', '@user1001']
  }, async ({ page }) => {
    // Navigate to GRO login page
    await page.goto('/groNg/#/login');

    // Wait for page to load
    await page.waitForLoadState('networkidle');

    // Verify login page elements are present
    await expect(page.locator('input[formcontrolname="userName"]')).toBeVisible();
    await expect(page.locator('input[formcontrolname="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();

    // Verify page title
    await expect(page).toHaveTitle(/GuidanceResources/);

    // Take a screenshot of the login page
    await page.screenshot({ path: 'test-results/gropr1-login-page.png', fullPage: true });
  });

  test('GROPR1 (User 1001) navigation and logout - Regression Test', {
    tag: ['@regression', '@prod', '@user1001']
  }, async ({ page }) => {
    // Login first
    await page.goto('/groNg/#/login');
    await page.waitForLoadState('networkidle');

    const username = 'GROPR1';
    const password = 'Testtest1';

    await page.locator('input[formcontrolname="userName"]').fill(username);
    await page.locator('input[formcontrolname="password"]').fill(password);
    await page.locator('button[type="submit"]').click();
    await page.waitForURL('**/home', { timeout: 30000 });

    // Verify successful login
    await expect(page).toHaveURL(/groNg\/#\/home/<USER>

    // Check if logout functionality is available (if logout button exists)
    const logoutButton = page.locator('a:has-text("Logout"), button:has-text("Logout"), [data-testid="logout"]');
    if (await logoutButton.count() > 0) {
      await logoutButton.click();
      await expect(page).toHaveURL(/login/);
    }

    // Take a screenshot after logout
    await page.screenshot({ path: 'test-results/gropr1-after-logout.png', fullPage: true });
  });
});
