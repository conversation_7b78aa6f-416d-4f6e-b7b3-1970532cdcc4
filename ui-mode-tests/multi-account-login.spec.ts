import { test, expect } from '@playwright/test';
import { getCredentialByWebId } from '../features/support/helpers';

// This file contains Playwright tests that can be run in UI mode
// These tests mirror the functionality in the Cucumber features

test.describe('Multi-Account Login Tests', () => {
  const webIds = ['RTX', 'pnc', 'aon', 'teconn', 'MacysIncEAP'];
  
  for (const webId of webIds) {
    test(`User can login with WebID: ${webId}`, async ({ page }) => {
      // Get credentials for this WebID
      const credential = getCredentialByWebId(webId);
      
      if (!credential) {
        test.skip(`No credentials found for WebID: ${webId}`);
        return;
      }
      
      // Navigate to login page
      await page.goto('/groNg/#/login');
      
      // Fill in login form
      await page.locator('input[formcontrolname="username"]').fill(credential.Username);
      await page.locator('input[formcontrolname="password"]').fill(credential.Password);
      
      // Click login button
      await page.locator('button[type="submit"]').click();
      
      // Wait for navigation to complete
      await page.waitForURL('**/home');
      
      // Check if user is logged in by looking for a common element on the home page
      const userMenuElement = page.locator('.navMenu');
      await expect(userMenuElement).toBeVisible();
      
      // Verify we're on the home page
      await expect(page).toHaveURL(new RegExp('groNg/#/home'));
      
      // Check for a specific element on the home page
      const headerElement = page.locator('h2[class=ng-star-inserted]');
      await expect(headerElement).toHaveText('Additional Tools & Resources');
    });
  }
});
