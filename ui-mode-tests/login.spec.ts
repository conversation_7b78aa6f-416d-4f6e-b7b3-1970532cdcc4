import { test, expect } from '@playwright/test';

// Simple login test for GROPR1 account in production environment

test.describe('Login Test - GROPR1 Account', () => {
  test('Debug: Inspect login page', async ({ page }) => {
    // Navigate to login page
    await page.goto('/groNg/#/login');

    // Wait for page to load
    await page.waitForLoadState('networkidle');

    // Wait a bit more for any dynamic content
    await page.waitForTimeout(3000);

    // Take a screenshot to see what's on the page
    await page.screenshot({ path: 'debug-login-page.png', fullPage: true });

    // Log the page title
    const title = await page.title();
    console.log('Page title:', title);

    // Log the current URL
    console.log('Current URL:', page.url());

    // Check if there are any input fields
    const inputs = await page.locator('input').count();
    console.log('Number of input fields found:', inputs);

    // Get all input elements and their attributes
    const inputElements = await page.locator('input').all();
    for (let i = 0; i < inputElements.length; i++) {
      const input = inputElements[i];
      const type = await input.getAttribute('type');
      const name = await input.getAttribute('name');
      const id = await input.getAttribute('id');
      const formControlName = await input.getAttribute('formcontrolname');
      const placeholder = await input.getAttribute('placeholder');
      const className = await input.getAttribute('class');
      console.log(`Input ${i}: type="${type}", name="${name}", id="${id}", formcontrolname="${formControlName}", placeholder="${placeholder}", class="${className}"`);
    }

    // Also check for any forms
    const forms = await page.locator('form').count();
    console.log('Number of forms found:', forms);

    // Check page content
    const bodyText = await page.locator('body').textContent();
    console.log('Page contains login text:', bodyText?.toLowerCase().includes('login'));
    console.log('Page contains username text:', bodyText?.toLowerCase().includes('username'));
    console.log('Page contains password text:', bodyText?.toLowerCase().includes('password'));
  });

  test('GROPR1 can login successfully', async ({ page }) => {
    // Navigate to login page
    await page.goto('/groNg/#/login');

    // Wait for page to load
    await page.waitForLoadState('networkidle');

    // Use GROPR1 credentials
    const username = 'GROPR1';
    const password = 'Testtest1';

    // Fill in login form with increased timeout
    await page.locator('input[formcontrolname="userName"]').fill(username, { timeout: 10000 });
    await page.locator('input[formcontrolname="password"]').fill(password, { timeout: 10000 });

    // Click login button
    await page.locator('button[type="submit"]').click({ timeout: 10000 });

    // Wait for navigation to complete with increased timeout
    await page.waitForURL('**/home', { timeout: 30000 });

    // Check if user is logged in by looking for a common element on the home page
    const userMenuElement = page.locator('.navMenu');
    await expect(userMenuElement).toBeVisible({ timeout: 15000 });

    // Verify we're on the home page
    await expect(page).toHaveURL(/groNg\/#\/home/<USER>
  });

  test('User can login with second account (rayth11)', async ({ page }) => {
    // Navigate to login page
    await page.goto('/groNg/#/login');

    // Use production credentials
    const username = 'rayth11';
    const password = 'Testtestp';

    // Wait for page to load
    await page.waitForLoadState('networkidle');

    // Fill in login form with increased timeout
    await page.locator('input[formcontrolname="username"]').fill(username, { timeout: 10000 });
    await page.locator('input[formcontrolname="password"]').fill(password, { timeout: 10000 });

    // Click login button
    await page.locator('button[type="submit"]').click({ timeout: 10000 });

    // Wait for navigation to complete with increased timeout
    await page.waitForURL('**/home', { timeout: 30000 });

    // Check if user is logged in by looking for a common element on the home page
    const userMenuElement = page.locator('.navMenu');
    await expect(userMenuElement).toBeVisible({ timeout: 15000 });

    // Verify we're on the home page
    await expect(page).toHaveURL(/groNg\/#\/home/<USER>

    // Check for a specific element on the home page
    const headerElement = page.locator('h2[class=ng-star-inserted]');
    await expect(headerElement).toHaveText('Additional Tools & Resources', { timeout: 15000 });
  });

  test('User can login with third account (produser3)', async ({ page }) => {
    // Navigate to login page
    await page.goto('/groNg/#/login');

    // Use production credentials
    const username = 'produser3';
    const password = 'ProdTest3';

    // Wait for page to load
    await page.waitForLoadState('networkidle');

    // Fill in login form with increased timeout
    await page.locator('input[formcontrolname="username"]').fill(username, { timeout: 10000 });
    await page.locator('input[formcontrolname="password"]').fill(password, { timeout: 10000 });

    // Click login button
    await page.locator('button[type="submit"]').click({ timeout: 10000 });

    // Wait for navigation to complete with increased timeout
    await page.waitForURL('**/home', { timeout: 30000 });

    // Check if user is logged in by looking for a common element on the home page
    const userMenuElement = page.locator('.navMenu');
    await expect(userMenuElement).toBeVisible({ timeout: 15000 });

    // Verify we're on the home page
    await expect(page).toHaveURL(/groNg\/#\/home/<USER>

    // Check for a specific element on the home page
    const headerElement = page.locator('h2[class=ng-star-inserted]');
    await expect(headerElement).toHaveText('Additional Tools & Resources', { timeout: 15000 });
  });
  
  test('User cannot login with invalid credentials', async ({ page }) => {
    // Navigate to login page
    await page.goto('/groNg/#/login');
    
    // Use invalid credentials
    await page.locator('input[formcontrolname="username"]').fill('invalid_user');
    await page.locator('input[formcontrolname="password"]').fill('invalid_password');
    
    // Click login button
    await page.locator('button[type="submit"]').click();
    
    // Check for error message
    const errorMessage = page.locator('.error-message');
    await expect(errorMessage).toBeVisible();
    await expect(errorMessage).toContainText(/invalid|failed|incorrect/i);
  });
});
