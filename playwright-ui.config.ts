import { PlaywrightTestConfig, devices } from '@playwright/test';
import * as path from 'path';

// Read environment variables
const ENV = process.env.TEST_ENV || 'prod';

// Base URL based on environment
const getBaseUrl = () => {
  switch (ENV) {
    case 'dev':
      return 'https://www.guidanceresources-dev.compsych-ad.int';
    case 'stg':
      return 'https://www.guidanceresources-stg.compsych-ad.int';
    case 'prod':
      return 'https://www.guidanceresources.com';
    default:
      return 'https://www.guidanceresources-stg.compsych-ad.int';
  }
};

// Configuration for UI mode
const config: PlaywrightTestConfig = {
  testDir: './ui-mode-tests',
  timeout: 60000,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: 1,
  reporter: [
    ['html', { outputFolder: 'playwright-report' }],
    ['list']
  ],
  use: {
    baseURL: getBaseUrl(),
    headless: false,
    viewport: { width: 1280, height: 720 },
    ignoreHTTPSErrors: true,
    video: 'on-first-retry',
    screenshot: 'only-on-failure',
    trace: 'on-first-retry',
    launchOptions: {
      slowMo: 100
    }
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] }
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] }
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] }
    }
  ],
  // Output directory for test artifacts
  outputDir: 'test-results/',
  // Folder for test snapshots
  snapshotDir: 'test-snapshots/',
  // Global setup file
  globalSetup: path.join(__dirname, 'global-setup.ts'),
  // Global teardown file
  globalTeardown: path.join(__dirname, 'global-teardown.ts')
};

export default config;



